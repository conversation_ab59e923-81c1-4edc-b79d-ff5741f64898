"use client";

import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { ProductService } from "@/services/product.service";
import { Product } from "@/data/models/product.model";
import { categories, country_currencies } from "@/lib/constants";
import { AddressService } from "@/services/address.service";
import { ExchangeRateService } from "@/services/exchange.service";
import { ShopSkeleton } from "@/components/skeletons/shop";
import ReactMarkdown from "react-markdown";

type Banner = {
  title: string;
  subtitle: string;
  productName: string;
  cta1: string;
  cta2: string;
  bgColor: string;
  textColor: string;
  image: string;
};

type Collection = {
  id: string;
  name: string;
};

type Category = {
  id: string;
  name: string;
  parent_id: string | null;
};

type CategoryHierarchy = {
  id: string;
  name: string;
  children: CategoryHierarchy[];
};

export default function Products() {
  // Category hierarchy utility functions
  const buildCategoryHierarchy = (
    categories: Category[],
  ): CategoryHierarchy[] => {
    const categoryMap = new Map<string, CategoryHierarchy>();
    const rootCategories: CategoryHierarchy[] = [];

    // First pass: create all category objects
    categories.forEach((category) => {
      categoryMap.set(category.id, {
        id: category.id,
        name: category.name,
        children: [],
      });
    });

    // Second pass: build hierarchy
    categories.forEach((category) => {
      const categoryNode = categoryMap.get(category.id)!;
      if (category.parent_id === null) {
        rootCategories.push(categoryNode);
      } else {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children.push(categoryNode);
        }
      }
    });

    return rootCategories;
  };

  const getRootCategories = (): Category[] => {
    return categories.filter((category) => category.parent_id === null);
  };

  const getChildCategories = (parentId: string): Category[] => {
    return categories.filter((category) => category.parent_id === parentId);
  };

  // State to manage Categories dropdown (for desktop)
  const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(),
  );

  // State for active category
  const [activeCategoryId, setActiveCategoryId] = useState<string | null>(null);

  // State for banner
  const [currentBanner, setCurrentBanner] = useState(0);

  // Fix: properly type the useRef for timers
  const autoScrollTimer = useRef<NodeJS.Timeout | null>(null);
  const autoScrollInterval = 5000; // 5 seconds between transitions

  // Banner data
  const banners: Banner[] = [
    {
      title: "Redefine Your Style Journey",
      subtitle: "Crafted With Intention",
      productName: "MailPallet",
      cta1: "View Product",
      cta2: "Shop Products Now",
      bgColor: "bg-black",
      textColor: "text-white",
      image:
        "https://pykbqzmmchcbcaijhkiq.supabase.co/storage/v1/object/public/mailpallet-assets/assets/MailPallet-Model.png",
    },
    {
      title: "Premium Tech",
      subtitle: "For Modern Living",
      productName: "Smart Watch Pro",
      cta1: "View Details",
      cta2: "Shop Collection",
      bgColor: "bg-black",
      textColor: "text-white",
      image: "https://placehold.co/500x600",
    },
    {
      title: "Sound Perfection",
      subtitle: "Ultimate Experience",
      productName: "Pro Earbuds",
      cta1: "Learn More",
      cta2: "Buy Now",
      bgColor: "bg-black",
      textColor: "text-white",
      image: "https://placehold.co/500x600",
    },
  ];

  // Setup auto-scrolling effect
  useEffect(() => {
    // Function to move to the next banner
    const nextBanner = () => {
      setCurrentBanner((prevBanner) =>
        prevBanner === banners.length - 1 ? 0 : prevBanner + 1,
      );
    };

    // Start the auto-scroll timer
    autoScrollTimer.current = setInterval(nextBanner, autoScrollInterval);

    // Clear the timer when component unmounts
    return () => {
      if (autoScrollTimer.current) {
        clearInterval(autoScrollTimer.current);
      }
    };
  }, [banners.length]); // Re-run if the number of banners changes

  // Reset timer when manually changing banners
  const handleBannerClick = (index: number) => {
    // Clear existing timer
    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
    }

    // Set the selected banner
    setCurrentBanner(index);

    // Restart the timer
    autoScrollTimer.current = setInterval(() => {
      setCurrentBanner((prevBanner) =>
        prevBanner === banners.length - 1 ? 0 : prevBanner + 1,
      );
    }, autoScrollInterval);
  };

  // Toggle Categories dropdown (for desktop)
  const toggleCategories = () => {
    setIsCategoriesOpen(!isCategoriesOpen);
  };

  // Toggle expanded state for category
  const toggleCategoryExpansion = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string | null) => {
    setActiveCategoryId(categoryId);
  };

  // Get root categories for navigation
  const rootCategories = getRootCategories();
  const categoryHierarchy = buildCategoryHierarchy(categories);

  // State for products, loading, and currency
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [userCurrencyCode, setUserCurrencyCode] = useState<string>("GBP");
  const [exchangeRate, setExchangeRate] = useState<number | null>(null);
  const [isLoadingExchangeRate, setIsLoadingExchangeRate] = useState(false);

  // Service instances
  const addressService = new AddressService();
  const exchangeRateService = new ExchangeRateService();

  // Helper function to get display pricing (considering variants)
  const getDisplayPricing = (product: Product) => {
    // If product has variants, use the first variant's pricing as display price
    // This gives users an idea of the price range
    if (product.variants?.variants && product.variants.variants.length > 0) {
      const firstVariant = product.variants.variants[0];
      return {
        price: firstVariant.price || product.primary_data.price,
        sale_price: firstVariant.sale_price || product.primary_data.sale_price,
        hasVariants: true,
      };
    }

    // For products without variants, use primary_data
    return {
      price: product.primary_data.price,
      sale_price: product.primary_data.sale_price,
      hasVariants: false,
    };
  };

  // Helper function to convert price using stored exchange rate
  const convertPrice = (price: number) => {
    if (userCurrencyCode === "GBP" || !exchangeRate) {
      return price;
    }
    return price * exchangeRate;
  };

  // Fetch exchange rate once and store it
  const fetchExchangeRate = async () => {
    if (userCurrencyCode === "GBP") {
      setExchangeRate(1);
      return;
    }

    setIsLoadingExchangeRate(true);
    try {
      const exchangeData =
        await exchangeRateService.getExchangeRateISO(userCurrencyCode);
      if (exchangeData && exchangeData.rate_per_gbp) {
        setExchangeRate(exchangeData.rate_per_gbp);
      } else {
        setExchangeRate(1); // Fallback to 1:1 rate
      }
    } catch (error) {
      console.error("Failed to fetch exchange rate:", error);
      setExchangeRate(1); // Fallback to 1:1 rate
    } finally {
      setIsLoadingExchangeRate(false);
    }
  };

  // Helper function for currency formatting
  const formatCurrency = (amount: number, fromCurrency: string) => {
    let symbol = "£";
    switch (fromCurrency) {
      case "GHS":
        symbol = "₵";
        break;
      case "NGN":
        symbol = "₦";
        break;
      default: // GBP and any other case
        symbol = "£";
    }

    return `${symbol} ${amount.toLocaleString("en-GB", {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    })}`;
  };

  // Helper function to get currency code by country name
  const getCurrencyCodeByCountryName = (countryName: string): string => {
    const currencyCode = Object.keys(country_currencies).find(
      (code) =>
        country_currencies[code as keyof typeof country_currencies] ===
        countryName,
    );
    return currencyCode || "GBP"; // Default to GBP if country not found
  };

  // Load user address and set currency code
  useEffect(() => {
    const loadUserAddress = async () => {
      try {
        const address = await addressService.fetchUserAddress();

        // Get currency code from country
        if (address.DeliverTo) {
          const currencyCode = getCurrencyCodeByCountryName(address.DeliverTo);
          setUserCurrencyCode(currencyCode);
        }
      } catch (error) {
        console.error("Failed to load user address:", error);
        // Default to GBP if address loading fails
        setUserCurrencyCode("GBP");
      }
    };

    loadUserAddress();
  }, []);

  // Fetch exchange rate when currency changes
  useEffect(() => {
    fetchExchangeRate();
  }, [userCurrencyCode]);

  // Fetch products with category filtering
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        const productService = new ProductService();
        // Determine if selected id is a root category or a subcategory
        const selectedCategory = categories.find(
          (c) => c.id === activeCategoryId,
        );
        const options = activeCategoryId
          ? selectedCategory && selectedCategory.parent_id
            ? { subcategory: activeCategoryId }
            : { category: activeCategoryId }
          : {};
        const { data } = await productService.listProducts(options);
        setProducts(data);
      } catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [activeCategoryId]);

  // Helper function to get stock status display
  const getStockStatus = (product: Product) => {
    // Safe check if product is internal and has stock information
    if (
      product.type === "internal" &&
      product.details &&
      "stock" in product.details &&
      typeof product.details.stock === "number"
    ) {
      const stockLevel = product.details.stock;

      if (stockLevel > 10) {
        return {
          text: "In Stock",
          classes: "bg-green-100 text-green-800",
        };
      } else if (stockLevel > 0) {
        return {
          text: `Only ${stockLevel} left`,
          classes: "bg-yellow-100 text-yellow-800",
        };
      } else {
        return {
          text: "Out of Stock",
          classes: "bg-red-100 text-red-800",
        };
      }
    }

    // Default return for external products or when stock info is missing
    return null;
  };

  return (
    <main className="max-w-7xl mx-auto px-3 sm:px-4 py-6 sm:py-8 lg:px-8">
      {/* Modified Hero Banner with Auto-Scroll - ElectronicZ Inspired - Hidden on mobile */}
      {/* <div className="hidden md:block w-full mb-8 md:mb-12 overflow-hidden rounded-lg shadow-xl relative">
        <div className="w-full bg-gradient-to-br from-black via-gray-900 to-blue-900 p-6 md:p-10 lg:p-16 relative overflow-hidden">
          <div className="absolute inset-0 opacity-20 bg-grid-pattern mix-blend-overlay"></div>

          <div className="flex flex-col lg:flex-row justify-between items-center relative z-10 lg:min-h-96">
            <div className="w-full lg:w-1/2 mb-8 lg:mb-0 pr-0 lg:pr-8">
              <div className="space-y-6">
                <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-white">
                  {banners[currentBanner].title}
                  <br />
                  {banners[currentBanner].subtitle}
                </h1>

                <h2 className="text-3xl font-medium text-white">{banners[currentBanner].productName}</h2>
              </div>
            </div>

            <div className="w-full lg:w-1/2 relative flex items-center justify-center">
              <div className="relative flex items-center justify-center w-full h-64 lg:h-96">
                <div className="absolute w-3/4 h-3/4 bg-blue-500 opacity-20 blur-3xl rounded-full"></div>
                <Image
                  src={banners[currentBanner].image}
                  alt="Banner product showcase"
                  width={500}
                  height={600}
                  className="object-contain max-h-full max-w-full transition-all duration-500 z-10"
                  style={{
                    filter: "drop-shadow(0 10px 25px rgba(59, 130, 246, 0.3))",
                    transform: "translateY(-10px)",
                    animation: "float 6s ease-in-out infinite",
                  }}
                />
                <div className="absolute bottom-0 w-1/2 h-12 bg-gradient-to-t from-blue-500/10 to-transparent blur-sm rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {banners.map((_, index) => (
            <button
              key={index}
              onClick={() => handleBannerClick(index)}
              className={`h-2 rounded-full transition-all ${currentBanner === index ? "bg-blue-500 w-8" : "bg-gray-400 hover:bg-gray-300 w-2"}`}
              aria-label={`Go to banner ${index + 1}`}
            />
          ))}
        </div>
      </div> */}

      {/* Mobile Horizontal Scrollable Categories - Improved spacing */}
      <div className="md:hidden mb-4 overflow-x-auto scrollbar-hide">
        <div className="flex space-x-2 sm:space-x-4 py-2 px-1 min-w-full">
          {/* All Products button */}
          <button
            className={`flex-shrink-0 py-1 sm:py-2 px-3 sm:px-4 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap transition-colors ${
              activeCategoryId === null
                ? "bg-gray-900 text-white"
                : "bg-gray-100 text-gray-800 hover:bg-gray-200"
            }`}
            onClick={() => handleCategorySelect(null)}
          >
            All Products
          </button>
          {/* Category buttons */}
          {rootCategories.map((category) => (
            <button
              key={category.id}
              className={`flex-shrink-0 py-1 sm:py-2 px-3 sm:px-4 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap transition-colors ${
                activeCategoryId === category.id
                  ? "bg-gray-900 text-white"
                  : "bg-gray-100 text-gray-800 hover:bg-gray-200"
              }`}
              onClick={() => handleCategorySelect(category.id)}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-8">
        {/* Sidebar Navigation - Desktop */}
        <div className="hidden md:block w-full md:w-64 flex-shrink-0">
          <nav className="space-y-4">
            {/* All Products */}
            <div className="pb-2">
              <button
                onClick={() => handleCategorySelect(null)}
                className={`block py-2 font-medium transition-colors w-full text-left ${
                  activeCategoryId === null
                    ? "text-blue-600"
                    : "text-gray-900 hover:text-gray-600"
                }`}
              >
                All Products
              </button>
            </div>

            {/* Categories */}
            <div className="pb-2 border-b">
              <div
                className="flex items-center justify-between py-2 font-medium cursor-pointer"
                onClick={toggleCategories}
              >
                <span>Categories</span>
                <svg
                  className={`w-4 h-4 transition-transform ${isCategoriesOpen ? "rotate-180" : ""}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </div>
            </div>

            {/* Category hierarchy with animation and vertical scroll when expanded */}
            <div
              className={`space-y-2 transition-all ${isCategoriesOpen ? "max-h-96 opacity-100 overflow-y-auto" : "max-h-0 opacity-0 overflow-hidden"}`}
            >
              {categoryHierarchy.map((category) => (
                <div key={category.id} className="space-y-1">
                  {/* Root category */}
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => handleCategorySelect(category.id)}
                      className={`flex-1 text-left py-1 font-medium transition-colors ${
                        activeCategoryId === category.id
                          ? "text-blue-600"
                          : "text-gray-900 hover:text-gray-600"
                      }`}
                    >
                      {category.name}
                    </button>
                    {category.children.length > 0 && (
                      <button
                        onClick={() => toggleCategoryExpansion(category.id)}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        <svg
                          className={`w-3 h-3 transition-transform ${expandedCategories.has(category.id) ? "rotate-90" : ""}`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </button>
                    )}
                  </div>

                  {/* Subcategories */}
                  {expandedCategories.has(category.id) &&
                    category.children.length > 0 && (
                      <div className="pl-4 space-y-1">
                        {category.children.map((subcategory) => (
                          <button
                            key={subcategory.id}
                            onClick={() => handleCategorySelect(subcategory.id)}
                            className={`flex items-center gap-2 py-1 w-full text-left transition-colors ${
                              activeCategoryId === subcategory.id
                                ? "text-blue-600"
                                : "text-gray-700 hover:text-gray-600"
                            }`}
                          >
                            <span className="text-gray-400">•</span>
                            <span className="text-sm">{subcategory.name}</span>
                          </button>
                        ))}
                      </div>
                    )}
                </div>
              ))}
            </div>
          </nav>
        </div>

        {/* Product Grid - Improved responsiveness */}
        <div className="flex-1">
          {loading ? (
            <ShopSkeleton />
          ) : products.length === 0 ? (
            <div className="py-12 text-center text-gray-500 h-screen">
              No products returned
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {products.map((product) => {
                // Get stock status information
                const stockStatus = getStockStatus(product);

                // Get display pricing (variant-aware)
                const pricing = getDisplayPricing(product);

                return (
                  <Link
                    key={product.id}
                    href={`/home/<USER>/products/${product.id}`}
                    className="group bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden border border-gray-100 block"
                  >
                    <div className="relative">
                      {/* Discount badge */}
                      {(() => {
                        const discountPercent =
                          pricing.sale_price &&
                          pricing.sale_price < pricing.price
                            ? Math.round(
                                ((pricing.price - pricing.sale_price) /
                                  pricing.price) *
                                  100,
                              )
                            : 0;
                        return (
                          discountPercent > 0 && (
                            <div className="absolute top-2 sm:top-3 left-2 sm:left-3 z-10 bg-red-500 text-white text-xs sm:text-sm font-bold py-1 px-2 sm:px-3 rounded-full flex items-center gap-1 transform rotate-2 shadow-sm">
                              <span>{discountPercent}%</span>
                              <span>OFF</span>
                            </div>
                          )
                        );
                      })()}

                      {/* Origin badge with flag-themed gradient */}
                      <div
                        className={`absolute top-2 sm:top-3 right-2 sm:right-3 z-10 text-xs font-bold py-1 px-2 sm:px-3 rounded-full shadow-sm ${
                          product.origin_location === "UK"
                            ? "bg-gradient-to-r from-blue-600 via-white to-red-600 text-white"
                            : "bg-gradient-to-r from-red-600 via-yellow-500 to-green-600 text-white"
                        }`}
                      >
                        {product.origin_location}
                      </div>

                      <div className="w-full h-48 sm:h-56 md:h-64 overflow-hidden">
                        <Image
                          src={
                            product.variants?.variants?.[0]?.images?.[0]?.url ||
                            product.primary_image?.url ||
                            "https://placehold.co/400x300"
                          }
                          alt={product.title}
                          width={400}
                          height={300}
                          className="w-full h-full object-contain object-center transform group-hover:scale-105 transition-transform duration-500"
                        />
                      </div>
                    </div>

                    <div className="p-3 sm:p-4">
                      {/* <div className="flex items-center space-x-1 mb-1">
                      <span className="text-xs font-medium text-gray-500 uppercase">{product.category}</span>
                      <span className="text-gray-300">•</span>
                      <span className="text-xs text-gray-500">{product.subcategory}</span>
                    </div> */}

                      <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-1 sm:mb-2 line-clamp-1 group-hover:text-blue-600 transition-colors">
                        {product.title}
                      </h3>

                      <div className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4 line-clamp-2">
                        <ReactMarkdown className="prose prose-sm max-w-none dark:prose-invert">
                          {product.description}
                        </ReactMarkdown>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-end gap-1 sm:gap-2">
                          {/* Sale price if available */}
                          {pricing.sale_price &&
                          pricing.sale_price < pricing.price ? (
                            <>
                              <span className="text-lg sm:text-xl font-bold text-gray-900">
                                {formatCurrency(
                                  convertPrice(pricing.sale_price),
                                  userCurrencyCode,
                                )}
                              </span>
                              <span className="text-sm sm:text-base text-gray-500 line-through">
                                {formatCurrency(
                                  convertPrice(pricing.price),
                                  userCurrencyCode,
                                )}
                              </span>
                            </>
                          ) : (
                            <span className="text-lg sm:text-xl font-bold text-gray-900">
                              {formatCurrency(
                                convertPrice(pricing.price),
                                userCurrencyCode,
                              )}
                            </span>
                          )}
                        </div>

                        {/* Stock indicator - Using our helper function */}
                        {stockStatus && (
                          <span
                            className={`text-xs px-2 py-1 rounded ${stockStatus.classes}`}
                          >
                            {stockStatus.text}
                          </span>
                        )}
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </main>
  );
}

/*
Add these styles to your globals.css file:

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-slideDown {
  animation: slideDown 0.3s ease-out forwards;
}
*/
