"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  useCart,
  useCartDispatch,
  removeFromCart,
  updateQuantity,
  clearCart,
} from "@/lib/CartContext";
import Image from "next/image";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, TriangleAlert } from "lucide-react";
import { CartProduct } from "@/lib/types";
import {
  ExternalProductDetails,
  InternalProductDetails,
} from "@/data/models/product.model";
import { StripePaymentOrder } from "@/components/app/stripe-payment-order";
import type { OrderItem, Currency } from "@/data/models/order.model";
import { UserService, UserData } from "@/services/user.service";
import { AddressService, UserAddressModel } from "@/services/address.service";
import { ExchangeRateService } from "@/services/exchange.service";
import { useAuth } from "@/hooks/use-auth";
import { country_currencies } from "@/lib/constants";

// Additional types for checkout
type GroupedCartItems = {
  [key: string]: CartProduct[];
};

type ShippingOption = {
  country?: string;
  zone?: string;
  rate?: number;
  duty?: number;
  currency?: string;
  days?: number;
};

const CheckoutPage: React.FC = () => {
  const cart = useCart();
  const dispatch = useCartDispatch();
  const router = useRouter();

  // Payment modal state
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [userProfile, setUserProfile] = useState<UserData | null>(null);
  const [isLoadingUser, setIsLoadingUser] = useState(true);

  // Address and currency conversion state
  const [userAddress, setUserAddress] = useState<UserAddressModel | null>(null);
  const [userCurrencyCode, setUserCurrencyCode] = useState<string>("GBP");
  const [convertedTotal, setConvertedTotal] = useState<number>(0);
  const [convertedTotalShipping, setConvertedTotalShipping] =
    useState<number>(0);
  const [convertedOriginSubtotals, setConvertedOriginSubtotals] = useState<{
    [key: string]: number;
  }>({});
  const [convertedOriginTotals, setConvertedOriginTotals] = useState<{
    [key: string]: number;
  }>({});
  const [convertedShippingRates, setConvertedShippingRates] = useState<{
    [key: string]: number;
  }>({});
  const [convertedDutyRates, setConvertedDutyRates] = useState<{
    [key: string]: number;
  }>({});
  const [convertedItemPrices, setConvertedItemPrices] = useState<{
    [key: string]: number;
  }>({});
  const [convertedItemSalePrices, setConvertedItemSalePrices] = useState<{
    [key: string]: number;
  }>({});
  const [isLoadingExchangeRate, setIsLoadingExchangeRate] = useState(false);
  const [exchangeRate, setExchangeRate] = useState<number | null>(null);

  // Service instances
  const userService = new UserService();
  const addressService = new AddressService();
  const exchangeRateService = new ExchangeRateService();
  // Auth guard for client-side checks
  const {
    isAuthenticated,
    redirectIfNotAuthenticated,
    loading: authLoading,
  } = useAuth();

  // Helper function to get pricing from cart product (considering variants)
  const getCartItemPricing = (item: CartProduct) => {
    // If there's a selected variant with pricing, use that; otherwise use primary_data
    if (item.selectedVariant?.price !== undefined) {
      return {
        price: item.selectedVariant.price,
        sale_price:
          item.selectedVariant.sale_price || item.selectedVariant.price,
        stock: item.selectedVariant.stock || 0,
      };
    }

    // Fallback to primary_data
    return {
      price: item.primary_data.price,
      sale_price: item.primary_data.sale_price || item.primary_data.price,
      stock: 0, // Stock not available at product level for internal products
    };
  };

  // Helper function to get currency code by country name
  const getCurrencyCodeByCountryName = (countryName: string): string => {
    const currencyCode = Object.keys(country_currencies).find(
      (code) =>
        country_currencies[code as keyof typeof country_currencies] ===
        countryName,
    );
    return currencyCode || "GBP"; // Default to GBP if country not found
  };

  const loadUserProfile = async () => {
    try {
      const profile = await userService.getUserProfile();
      setUserProfile(profile);
    } catch (error) {
      console.error("Failed to load user profile:", error);
      // Handle error - maybe redirect to login or show error message
    } finally {
      setIsLoadingUser(false);
    }
  };

  const loadUserAddress = async () => {
    try {
      const address = await addressService.fetchUserAddress();
      setUserAddress(address);

      // Get currency code from country
      if (address.DeliverTo) {
        const currencyCode = getCurrencyCodeByCountryName(address.DeliverTo);
        setUserCurrencyCode(currencyCode);
      }
    } catch (error) {
      console.error("Failed to load user address:", error);
      // Default to GBP if address loading fails
      setUserCurrencyCode("GBP");
    }
  };

  // Fetch exchange rate once and store it
  const fetchExchangeRate = async () => {
    if (userCurrencyCode === "GBP") {
      setExchangeRate(1);
      return;
    }

    setIsLoadingExchangeRate(true);
    try {
      const exchangeData =
        await exchangeRateService.getExchangeRateISO(userCurrencyCode);
      if (exchangeData && exchangeData.rate_per_gbp) {
        setExchangeRate(exchangeData.rate_per_gbp);
      } else {
        setExchangeRate(1); // Fallback to 1:1 rate
      }
    } catch (error) {
      console.error("Failed to fetch exchange rate:", error);
      setExchangeRate(1); // Fallback to 1:1 rate
    } finally {
      setIsLoadingExchangeRate(false);
    }
  };

  // Calculate converted values using stored exchange rate
  const calculateConvertedValues = (
    currentTotal: number,
    totalShipping: number,
    originSubtotals: { [key: string]: number },
    shippingRates: { [key: string]: number },
    dutyRates: { [key: string]: number },
    itemPrices: { [key: string]: number },
    itemSalePrices: { [key: string]: number },
  ) => {
    if (!exchangeRate) return;

    if (currentTotal === 0) {
      // Reset all converted values to 0
      setConvertedTotal(0);
      setConvertedTotalShipping(0);
      setConvertedOriginSubtotals({});
      setConvertedOriginTotals({});
      setConvertedShippingRates({});
      setConvertedItemPrices({});
      setConvertedItemSalePrices({});
      return;
    }

    // Convert grand total and shipping
    setConvertedTotal(currentTotal * exchangeRate);
    setConvertedTotalShipping(totalShipping * exchangeRate);

    // Convert origin subtotals
    const convertedSubtotals: { [key: string]: number } = {};
    Object.keys(originSubtotals).forEach((origin) => {
      convertedSubtotals[origin] = originSubtotals[origin] * exchangeRate;
    });
    setConvertedOriginSubtotals(convertedSubtotals);

    // Convert shipping rates
    const convertedShipping: { [key: string]: number } = {};
    Object.keys(shippingRates).forEach((origin) => {
      convertedShipping[origin] = shippingRates[origin] * exchangeRate;
    });
    setConvertedShippingRates(convertedShipping);

    // Convert duty rates
    const convertedDuties: { [key: string]: number } = {};
    Object.keys(dutyRates).forEach((origin) => {
      convertedDuties[origin] = dutyRates[origin] * exchangeRate;
    });
    setConvertedDutyRates(convertedDuties);

    // Convert origin totals (subtotal + shipping for each origin)
    const convertedOriginTotalsCalc: { [key: string]: number } = {};
    Object.keys(originSubtotals).forEach((origin) => {
      convertedOriginTotalsCalc[origin] =
        (originSubtotals[origin] +
          (shippingRates[origin] || 0) +
          (dutyRates[origin] || 0)) *
        exchangeRate;
    });
    setConvertedOriginTotals(convertedOriginTotalsCalc);

    // Convert item prices
    const convertedPrices: { [key: string]: number } = {};
    Object.keys(itemPrices).forEach((itemId) => {
      convertedPrices[itemId] = itemPrices[itemId] * exchangeRate;
    });
    setConvertedItemPrices(convertedPrices);

    // Convert item sale prices
    const convertedSalePrices: { [key: string]: number } = {};
    Object.keys(itemSalePrices).forEach((itemId) => {
      convertedSalePrices[itemId] = itemSalePrices[itemId] * exchangeRate;
    });
    setConvertedItemSalePrices(convertedSalePrices);
  };

  // Load user profile and address on component mount (guard user profile by auth)
  useEffect(() => {
    // If auth status is known and user is not authenticated, redirect to login
    if (!authLoading && !isAuthenticated) {
      redirectIfNotAuthenticated();
      return;
    }

    // Only load user profile when authenticated
    if (isAuthenticated) {
      loadUserProfile();
    }

    // Address can be loaded for guests (guest-safe GET returns empty object),
    // so we still load it to allow currency/locale fallbacks.
    loadUserAddress();
  }, [authLoading, isAuthenticated]);

  // Fetch exchange rate when currency changes
  useEffect(() => {
    fetchExchangeRate();
  }, [userCurrencyCode]);

  // Recalculate converted values when exchange rate or cart changes
  useEffect(() => {
    if (!exchangeRate) return;

    if (cart.items.length === 0) {
      // Reset all converted values when cart is empty
      setConvertedTotal(0);
      setConvertedTotalShipping(0);
      setConvertedOriginSubtotals({});
      setConvertedOriginTotals({});
      setConvertedShippingRates({});
      setConvertedItemPrices({});
      setConvertedItemSalePrices({});
      return;
    }

    if (cart.items.length > 0) {
      const cartItems = cart.items;

      // Calculate subtotal (items only)
      const subtotalOnly = cartItems.reduce((sum, item) => {
        const pricing = getCartItemPricing(item);
        return sum + pricing.sale_price * item.quantity;
      }, 0);

      // Group items by origin
      const groupedItems = cartItems.reduce((acc, item) => {
        const origin = item.origin_location;
        if (!acc[origin]) acc[origin] = [];
        acc[origin].push(item);
        return acc;
      }, {} as GroupedCartItems);

      // Calculate subtotals per origin
      const subtotals = Object.keys(groupedItems).reduce(
        (acc, origin) => {
          acc[origin] = groupedItems[origin].reduce((sum, item) => {
            const pricing = getCartItemPricing(item);
            return sum + pricing.sale_price * item.quantity;
          }, 0);
          return acc;
        },
        {} as { [key: string]: number },
      );

      // Calculate shipping rates
      const shippingRates: { [key: string]: number } = {};
      const dutyRates: { [key: string]: number } = {};
      Object.keys(shippingOptions).forEach((origin) => {
        shippingRates[origin] = shippingOptions[origin]?.rate || 0;
        dutyRates[origin] = shippingOptions[origin]?.duty || 0;
      });

      const totalShipping =
        Object.values(shippingRates).reduce((sum, rate) => sum + rate, 0) +
        Object.values(dutyRates).reduce((sum, rate) => sum + rate, 0);

      // Calculate grand total (subtotal + shipping)
      const grandTotal = subtotalOnly + totalShipping;

      // Create item prices objects
      const itemPrices: { [key: string]: number } = {};
      const itemSalePrices: { [key: string]: number } = {};

      cartItems.forEach((item) => {
        const pricing = getCartItemPricing(item);
        itemPrices[item.id] = pricing.price;
        itemSalePrices[item.id] = pricing.sale_price;
      });

      calculateConvertedValues(
        grandTotal,
        totalShipping,
        subtotals,
        shippingRates,
        dutyRates,
        itemPrices,
        itemSalePrices,
      );
    }
  }, [exchangeRate, cart.items]);

  // Get cart items from context
  const cartItems = cart.items;

  // Use actual cart items from context
  const displayItems = cartItems;

  // Group items by origin
  const groupedItems: GroupedCartItems = displayItems.reduce((acc, item) => {
    const origin = item.origin_location;
    if (!acc[origin]) {
      acc[origin] = [];
    }
    acc[origin].push(item);
    return acc;
  }, {} as GroupedCartItems);

  // Set all origins as expanded by default
  const expandedOrigins = Object.keys(groupedItems).reduce((acc, origin) => {
    acc[origin] = true;
    return acc;
  }, {} as {[key: string]: boolean});

  // Calculate subtotals per origin
  const subtotals = Object.keys(groupedItems).reduce((acc, origin) => {
    acc[origin] = groupedItems[origin].reduce((sum, item) => {
      const pricing = getCartItemPricing(item);
      return sum + (pricing.sale_price * item.quantity);
    }, 0);
    return acc;
  }, {} as {[key: string]: number});

  // Initialize shipping options based on user location
  const shippingOptions = {} as { [key: string]: ShippingOption | null };
  Object.keys(groupedItems).forEach((origin) => {
    // Get first product from this origin
    const firstProduct = groupedItems[origin][0];

    // Find the relevant shipping rate for the user's location
    const userCountry = userAddress?.DeliverTo || 'United Kingdom';

    // Check if shipping_rates is NULL
    if (!firstProduct.shipping_rates) {
      // Set a null shipping option to indicate shipping info is not available
      shippingOptions[origin] = null;
      return;
    }

    const relevantRate = firstProduct.shipping_rates.find(
      rate => rate.to_country === userCountry || rate.to_zone === userCountry
    ) || firstProduct.shipping_rates[0];

    shippingOptions[origin] = {
      country: relevantRate?.to_country,
      zone: relevantRate?.to_zone,
      rate: relevantRate?.base_rate,
      duty: (relevantRate as any)?.duty,
      currency: relevantRate?.currency,
      days: relevantRate?.estimated_delivery_days,
    };
  });

  // Calculate total
  const total = Object.keys(subtotals).reduce((sum, origin) => {
    const shippingRate = shippingOptions[origin]
      ? shippingOptions[origin]?.rate || 0
      : 0;
    const dutyRate = shippingOptions[origin]
      ? shippingOptions[origin]?.duty || 0
      : 0;
    return sum + subtotals[origin] + shippingRate + dutyRate;
  }, 0);

  // Fetch exchange rate when currency code or total changes
  useEffect(() => {
    if (userCurrencyCode && total > 0) {
      // Calculate total shipping
      const totalShipping = Object.values(shippingOptions).reduce((sum, option) =>
        sum + (option ? (option.rate || 0) + (option.duty || 0) : 0), 0);

      // Create shipping rates object by origin
      const shippingRates: { [key: string]: number } = {};
      const dutyRates: { [key: string]: number } = {};
      Object.keys(shippingOptions).forEach((origin) => {
        shippingRates[origin] = shippingOptions[origin]?.rate || 0;
        dutyRates[origin] = shippingOptions[origin]?.duty || 0;
      });

      // Create item prices and sale prices objects
      const itemPrices: { [key: string]: number } = {};
      const itemSalePrices: { [key: string]: number } = {};

      cartItems.forEach((item) => {
        const pricing = getCartItemPricing(item);
        itemPrices[item.id] = pricing.price;
        itemSalePrices[item.id] = pricing.sale_price;
      });

      calculateConvertedValues(
        total,
        totalShipping!,
        subtotals,
        shippingRates,
        dutyRates,
        itemPrices,
        itemSalePrices,
      );
    }
  }, [userCurrencyCode, total]);

  // UI-only helper functions
  const getOriginalPriceString = (item: CartProduct): string => {
    let symbol = "£";
    if (item.currency === "GHS") symbol = "₵";
    if (item.currency === "NGN") symbol = "₦";

    const pricing = getCartItemPricing(item);
    return `${symbol}${pricing.sale_price.toFixed(2)} ${item.currency}`;
  };

  const getOriginalShippingString = (origin: string): string => {
    const option = shippingOptions[origin];
    if (!option) return 'Not available';

    let symbol = "£";
    if (option.currency === "GHS") symbol = "₵";
    if (option.currency === "NGN") symbol = "₦";

    return `${symbol}${option.rate?.toFixed(2)} ${option.currency}`;
  };

  // UI-only display helper
  const formatCurrency = (amount: number, fromCurrency: string) => {
    let symbol = "£";
    switch (fromCurrency) {
      case "GHS":
        symbol = "₵";
        break;
      case "NGN":
        symbol = "₦";
        break;
      default: // GBP and any other case
        symbol = "£";
    }

    return `${symbol} ${amount.toLocaleString("en-GB", {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    })}`;
  };

  // Transform cart items to order items for Stripe payment
  const transformCartToOrderItems = (): OrderItem[] => {
    return cartItems.map((item) => {
      const pricing = getCartItemPricing(item);

      // Create base order item
      const orderItem: OrderItem = {
        id: item.id,
        product_id: item.id,
        product_snapshot: {
          title: item.title,
          description: item.description,
          category: item.category,
          subcategory: item.subcategory,
          refundable: item.refundable,
          condition: item.condition,
          sku: item.type === "internal" ? item.id : item.id,
          weight_kg:
            item.type === "internal"
              ? (item.details as InternalProductDetails).weight_kg
              : undefined,
          dimensions_cm:
            item.type === "internal" &&
            (item.details as InternalProductDetails).dimensions_cm &&
            (item.details as InternalProductDetails).dimensions_cm!.length !==
              undefined &&
            (item.details as InternalProductDetails).dimensions_cm!.width !==
              undefined &&
            (item.details as InternalProductDetails).dimensions_cm!.height !==
              undefined
              ? {
                  length: (item.details as InternalProductDetails)
                    .dimensions_cm!.length as number,
                  width: (item.details as InternalProductDetails).dimensions_cm!
                    .width as number,
                  height: (item.details as InternalProductDetails)
                    .dimensions_cm!.height as number,
                }
              : undefined,
        },
        quantity: item.quantity,
        unit_price: pricing.sale_price,
        line_total: pricing.sale_price * item.quantity,
        currency: item.currency as Currency,
        warehouse_location: item.origin_location,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Add variant information if this is a variant product
      if (item.selectedVariant && item.selectedVariant.variant_id) {
        orderItem.variant_id = item.selectedVariant.variant_id;
        orderItem.variant_snapshot = {
          variant_id: item.selectedVariant.variant_id,
          option_values: item.selectedVariantOptions || {},
          sku: item.selectedVariant.sku,
          image:
            item.selectedVariant.images &&
            item.selectedVariant.images.length > 0
              ? item.selectedVariant.images[0]
              : { url: "", name: "" },
          price: item.selectedVariant.price || pricing.price,
          sale_price: item.selectedVariant.sale_price,
          stock: item.selectedVariant.stock || 0,
        };
      }

      return orderItem;
    });
  };

  // Calculate totals for payment
  const calculatePaymentTotals = () => {
    const orderItems = transformCartToOrderItems();
    const subtotalAmount = orderItems.reduce((sum, item) => sum + item.line_total, 0);
    const shippingAmount = Object.values(shippingOptions).reduce((sum, option) =>
      sum + (option ? option.rate || 0 : 0), 0);
    const taxAmount = 0;
    const shippingDuty = Object.values(shippingOptions).reduce((sum, option) => sum + (option ? option.duty || 0 : 0), 0);
    const totalAmount = subtotalAmount + shippingAmount + shippingDuty;
    return {
      orderItems,
      subtotal: subtotalAmount,
      taxAmount,
      shippingCost: shippingAmount,
      totalAmount,
      currency: "GBP" as Currency,
    };
  };

  // Handle opening payment modal
  const handleProceedToPayment = () => {
    if (!userProfile) {
      // If the user is not authenticated, redirect them to login and preserve the
      // next parameter so they return to the checkout after signing in.
      router.push(`/login?next=${encodeURIComponent("/home/<USER>/cart")}`);
      return;
    }
    setIsPaymentModalOpen(true);
  };

  // Handle closing payment modal
  const handleClosePaymentModal = () => {
    setIsPaymentModalOpen(false);
  };

  // Handle successful payment completion
  const handlePaymentSuccess = () => {
    // Clear the cart
    clearCart(dispatch);
    // Close the payment modal
    setIsPaymentModalOpen(false);
    // Redirect to my-orders page
    router.push("/home/<USER>/my-orders");
  };

  // Helper function to check if all address flags are null
  const areAllAddressFlagsNull = (): boolean => {
    return (
      userAddress !== null &&
      userAddress.WithinAccra === null &&
      userAddress.OutsideAccra === null &&
      userAddress.WithinLagos === null &&
      userAddress.OutsideLagos === null
    );
  };

  return (
    <div className="container mx-auto px-4 py-6 sm:py-8">
      <h1 className="text-2xl sm:text-3xl font-bold mb-6 sm:mb-8">Checkout</h1>

      {/* Empty cart state */}
      {displayItems.length === 0 ? (
        <div className="text-center py-8 sm:py-12">
          <h2 className="text-xl font-medium mb-4">Your cart is empty</h2>
          <p className="text-gray-600 mb-6">
            Add some items to your cart to get started.
          </p>
          <Link
            href="/home/<USER>"
            className="inline-block px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Continue Shopping
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Left side - Cart items grouped by origin */}
          <div className="lg:col-span-2 space-y-6">
            {Object.keys(groupedItems).map(origin => (
              <div key={origin} className="border rounded-lg overflow-hidden shadow-sm">
                <div
                  className="bg-gray-100 px-4 py-3 flex justify-between items-center cursor-pointer"
                >
                  <h2 className="font-bold text-base sm:text-lg">
                    {origin} ({groupedItems[origin].length} item
                    {groupedItems[origin].length !== 1 ? "s" : ""})
                  </h2>
                  <span>
                    {expandedOrigins[origin] ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-5 h-5"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M19.5 12h-15"
                        />
                      </svg>
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-5 h-5"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M12 4.5v15m7.5-7.5h-15"
                        />
                      </svg>
                    )}
                  </span>
                </div>

                {expandedOrigins[origin] && (
                  <div className="divide-y">
                    {groupedItems[origin].map((item) => (
                      <div
                        key={item.id}
                        className="p-4 flex flex-col sm:flex-row"
                      >
                        <div className="relative w-20 h-20 sm:w-24 sm:h-24 sm:mr-4 mb-4 sm:mb-0 flex-shrink-0">
                          {/* Placeholder for image */}
                          <Image
                            src={
                              item.selectedVariant?.images?.[0]?.url ||
                              item.primary_image?.url ||
                              "/placeholder-image.png"
                            }
                            alt={item.title}
                            className="object-center object-contain w-full h-full rounded"
                            width={96}
                            height={96}
                            onError={(e) => {
                              (e.target as HTMLImageElement).src =
                                "/placeholder-image.png";
                            }}
                          />
                        </div>

                        <div className="flex-1">
                          <div className="flex flex-col sm:flex-row sm:justify-between mb-2">
                            <h3 className="font-medium text-base sm:text-lg mb-1 sm:mb-0">
                              {item.title}
                            </h3>
                            <div className="text-left sm:text-right">
                              <div>
                                {isLoadingExchangeRate &&
                                userCurrencyCode !== "GBP" ? (
                                  <div className="flex flex-col items-end gap-1">
                                    {(() => {
                                      const pricing = getCartItemPricing(item);
                                      return pricing.sale_price < pricing.price;
                                    })() ? (
                                      <>
                                        <Skeleton className="h-4 w-20" />
                                        <Skeleton className="h-5 w-24" />
                                      </>
                                    ) : (
                                      <Skeleton className="h-5 w-24" />
                                    )}
                                  </div>
                                ) : (
                                  <>
                                    {(() => {
                                      const pricing = getCartItemPricing(item);
                                      return pricing.sale_price <
                                        pricing.price ? (
                                        <>
                                          <span className="text-gray-500 line-through text-sm mr-2">
                                            {userCurrencyCode !== "GBP" &&
                                            convertedItemPrices[item.id]
                                              ? formatCurrency(
                                                  convertedItemPrices[item.id],
                                                  userCurrencyCode,
                                                )
                                              : formatCurrency(
                                                  pricing.price,
                                                  item.currency,
                                                )}
                                          </span>
                                          <span className="font-bold">
                                            {userCurrencyCode !== "GBP" &&
                                            convertedItemSalePrices[item.id]
                                              ? formatCurrency(
                                                  convertedItemSalePrices[
                                                    item.id
                                                  ],
                                                  userCurrencyCode,
                                                )
                                              : formatCurrency(
                                                  pricing.sale_price,
                                                  item.currency,
                                                )}
                                          </span>
                                        </>
                                      ) : (
                                        <span className="font-bold">
                                          {userCurrencyCode !== "GBP" &&
                                          convertedItemSalePrices[item.id]
                                            ? formatCurrency(
                                                convertedItemSalePrices[
                                                  item.id
                                                ],
                                                userCurrencyCode,
                                              )
                                            : formatCurrency(
                                                pricing.sale_price,
                                                item.currency,
                                              )}
                                        </span>
                                      );
                                    })()}
                                  </>
                                )}
                              </div>
                              {(() => {
                                const pricing = getCartItemPricing(item);
                                const discountPercent =
                                  pricing.sale_price < pricing.price
                                    ? Math.round(
                                        ((pricing.price - pricing.sale_price) /
                                          pricing.price) *
                                          100,
                                      )
                                    : 0;
                                return (
                                  discountPercent > 0 && (
                                    <span className="text-green-600 text-sm">
                                      Save {discountPercent}%
                                    </span>
                                  )
                                );
                              })()}
                            </div>
                          </div>

                          {/* Variant Information */}
                          {item.selectedVariantOptions &&
                            Object.keys(item.selectedVariantOptions).length >
                              0 && (
                              <div className="text-sm text-gray-600 mb-2">
                                {Object.entries(
                                  item.selectedVariantOptions,
                                ).map(([key, value]) => (
                                  <span
                                    key={key}
                                    className="inline-block bg-gray-100 rounded px-2 py-1 mr-2 mb-1"
                                  >
                                    {key}: {value}
                                  </span>
                                ))}
                              </div>
                            )}

                          <div className="text-sm text-gray-600 mb-3">
                            {item.type === "internal" ? (
                              <span>
                                In Stock:{" "}
                                {getCartItemPricing(item).stock || "Available"}
                              </span>
                            ) : (
                              <span>
                                From:{" "}
                                {
                                  (item.details as ExternalProductDetails)
                                    .origin_name
                                }
                              </span>
                            )}
                            {" • "}
                            <span>Condition: {item.condition}</span>
                          </div>

                          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-0">
                            <div className="flex items-center">
                              <button
                                className="w-8 h-8 flex items-center justify-center border rounded-l hover:bg-gray-100"
                                disabled={item.quantity <= 1}
                                onClick={() =>
                                  updateQuantity(
                                    dispatch,
                                    item,
                                    item.quantity - 1,
                                  )
                                }
                              >
                                -
                              </button>
                              <input
                                type="number"
                                value={item.quantity}
                                className="w-12 h-8 text-center border-t border-b"
                                min="1"
                                onChange={(e) => {
                                  const newQuantity =
                                    parseInt(e.target.value) || 1;
                                  updateQuantity(dispatch, item, newQuantity);
                                }}
                              />
                              <button
                                className="w-8 h-8 flex items-center justify-center border rounded-r hover:bg-gray-100"
                                onClick={() =>
                                  updateQuantity(
                                    dispatch,
                                    item,
                                    item.quantity + 1,
                                  )
                                }
                              >
                                +
                              </button>
                            </div>

                            <button
                              className="text-red-500 hover:text-red-700 text-sm"
                              onClick={() => removeFromCart(dispatch, item)}
                            >
                              Remove
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Shipping information for this origin */}
                    <div className="p-4">
                      <h3 className="font-medium mb-2">
                        Shipping from {origin}
                      </h3>
                      {shippingOptions[origin] === null ? (
                        <div className="bg-yellow-50 border border-yellow-100 rounded p-3 text-sm">
                          <div className="font-medium text-yellow-700">
                            Shipping information unavailable
                          </div>
                          <div className="mt-1 text-gray-700">
                            Shipping rates for this product are not available.
                            Please contact customer support for more
                            information.
                          </div>
                        </div>
                      ) : (
                        <div className="bg-blue-50 border border-blue-100 rounded p-3 text-sm">
                          <div className="font-medium">
                            Delivery to {shippingOptions[origin]?.country} (
                            {shippingOptions[origin]?.zone})
                          </div>
                          <div className="mt-1 text-gray-700">
                            {isLoadingExchangeRate &&
                            userCurrencyCode !== "GBP" ? (
                              <div className="flex items-center gap-2">
                                <Skeleton className="h-4 w-20" />
                                <span>•</span>
                                <span>
                                  Estimated delivery in{" "}
                                  {shippingOptions[origin]?.days || "N/A"} days
                                </span>
                              </div>
                            ) : (
                              <>
                                {userCurrencyCode !== "GBP" &&
                                convertedShippingRates[origin]
                                  ? formatCurrency(
                                      convertedShippingRates[origin],
                                      userCurrencyCode,
                                    )
                                  : formatCurrency(
                                      shippingOptions[origin]?.rate || 0,
                                      shippingOptions[origin]?.currency ||
                                        "GBP",
                                    )}{" "}
                                • Estimated delivery in{" "}
                                {shippingOptions[origin]?.days || "N/A"} days
                              </>
                            )}
                          </div>
                          {shippingOptions[origin] === null ||
                            (shippingOptions[origin]?.rate === 0 && (
                              <div className="text-yellow-600 text-sm ml-1">
                                (some rates unavailable)
                              </div>
                            ))}
                        </div>
                      )}
                    </div>

                    {/* Subtotal for this origin */}
                    <div className="p-4 bg-gray-50">
                      <div className="flex justify-between mb-1">
                        <span>
                          Subtotal (
                          {groupedItems[origin].reduce(
                            (sum, item) => sum + item.quantity,
                            0,
                          )}{" "}
                          items):
                        </span>
                        <span>
                          {isLoadingExchangeRate &&
                          userCurrencyCode !== "GBP" ? (
                            <Skeleton className="h-4 w-20" />
                          ) : userCurrencyCode !== "GBP" &&
                            convertedOriginSubtotals[origin] ? (
                            formatCurrency(
                              convertedOriginSubtotals[origin],
                              userCurrencyCode,
                            )
                          ) : (
                            formatCurrency(
                              subtotals[origin],
                              groupedItems[origin][0].currency,
                            )
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between mb-1">
                        <span>Shipping:</span>
                        <span>
                          {isLoadingExchangeRate &&
                          userCurrencyCode !== "GBP" ? (
                            <Skeleton className="h-4 w-20" />
                          ) : userCurrencyCode !== "GBP" &&
                            convertedShippingRates[origin] ? (
                            formatCurrency(
                              convertedShippingRates[origin],
                              userCurrencyCode,
                            )
                          ) : (
                            formatCurrency(
                              shippingOptions[origin]?.rate || 0,
                              shippingOptions[origin]?.currency || "GBP",
                            )
                          )}
                          {Object.values(shippingOptions).some(
                            (option) => option === null || option.rate === 0,
                          ) && (
                            <span className="text-yellow-600 text-sm ml-1">
                              (some rates unavailable)
                            </span>
                          )}
                        </span>
                      </div>
                      {shippingOptions[origin]?.duty ? (
                        <div className="flex justify-between mb-1">
                          <span>Duty:</span>
                          <span>
                            {isLoadingExchangeRate &&
                            userCurrencyCode !== "GBP" ? (
                              <Skeleton className="h-4 w-20" />
                            ) : userCurrencyCode !== "GBP" &&
                              convertedDutyRates[origin] ? (
                              formatCurrency(
                                convertedDutyRates[origin],
                                userCurrencyCode,
                              )
                            ) : (
                              formatCurrency(
                                shippingOptions[origin]?.duty || 0,
                                shippingOptions[origin]?.currency || "GBP",
                              )
                            )}
                          </span>
                        </div>
                      ) : null}
                      <div className="flex justify-between font-bold">
                        <span>Total from {origin}:</span>
                        <span>
                          {shippingOptions[origin] === null ? (
                            <span>
                              {isLoadingExchangeRate &&
                              userCurrencyCode !== "GBP" ? (
                                <Skeleton className="h-5 w-24" />
                              ) : userCurrencyCode !== "GBP" &&
                                convertedOriginSubtotals[origin] ? (
                                formatCurrency(
                                  convertedOriginSubtotals[origin],
                                  userCurrencyCode,
                                )
                              ) : (
                                formatCurrency(
                                  subtotals[origin],
                                  groupedItems[origin][0].currency,
                                )
                              )}
                              <span className="text-yellow-600 text-sm ml-1">
                                (+shipping not available)
                              </span>
                            </span>
                          ) : isLoadingExchangeRate &&
                            userCurrencyCode !== "GBP" ? (
                            <Skeleton className="h-5 w-24" />
                          ) : userCurrencyCode !== "GBP" &&
                            convertedOriginTotals[origin] ? (
                            formatCurrency(
                              convertedOriginTotals[origin],
                              userCurrencyCode,
                            )
                          ) : (
                            formatCurrency(
                              subtotals[origin] +
                                (shippingOptions[origin]?.rate || 0) +
                                (shippingOptions[origin]?.duty || 0),
                              groupedItems[origin][0].currency,
                            )
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Right side - Order summary */}
          <div className="lg:col-span-1">
            <div className="border rounded-lg p-4 shadow-sm lg:sticky lg:top-4">
              <h2 className="text-xl font-bold mb-4">Order Summary</h2>

              {/* Alert when all address flags are NULL */}
              {areAllAddressFlagsNull() && (
                <Alert className="border-orange-200 bg-orange-50 mb-4">
                  <AlertDescription className="text-orange-800">
                    Please update your address details to proceed with checkout.
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2 mb-4">
                {Object.keys(groupedItems).map((origin) => (
                  <div
                    key={`summary-${origin}`}
                    className="flex justify-between"
                  >
                    <span>{origin} Subtotal:</span>
                    <span>
                      {isLoadingExchangeRate && userCurrencyCode !== "GBP" ? (
                        <Skeleton className="h-4 w-20" />
                      ) : userCurrencyCode !== "GBP" &&
                        convertedOriginSubtotals[origin] ? (
                        formatCurrency(
                          convertedOriginSubtotals[origin],
                          userCurrencyCode,
                        )
                      ) : (
                        formatCurrency(
                          subtotals[origin],
                          groupedItems[origin][0].currency,
                        )
                      )}
                    </span>
                  </div>
                ))}

                <div className="border-t my-2 pt-2">
                  <div className="flex justify-between">
                    <span>Total Shipping (inc. of duty):</span>
                    <span>
                      {isLoadingExchangeRate && userCurrencyCode !== "GBP" ? (
                        <Skeleton className="h-4 w-20" />
                      ) : userCurrencyCode !== "GBP" &&
                        convertedTotalShipping ? (
                        formatCurrency(convertedTotalShipping, userCurrencyCode)
                      ) : (
                        formatCurrency(
                          Object.values(shippingOptions).reduce(
                            (sum, option) =>
                              sum + (option ? option.rate || 0 : 0),
                            0,
                          ),
                          "GBP",
                        )
                      )}
                      {Object.values(shippingOptions).some(
                        (option) => option === null || option.rate === 0,
                      ) && (
                        <span className="text-yellow-600 text-sm ml-1">
                          (some rates unavailable)
                        </span>
                      )}
                    </span>
                  </div>
                </div>

                <div className="border-t my-2 pt-2 text-xl font-bold">
                  <div className="flex justify-between">
                    <span>Grand Total:</span>
                    <span>
                      {isLoadingExchangeRate ? (
                        <Skeleton className="h-4 w-[150px]" />
                      ) : userCurrencyCode !== "GBP" && convertedTotal > 0 ? (
                        formatCurrency(convertedTotal, userCurrencyCode)
                      ) : (
                        formatCurrency(total, userCurrencyCode)
                      )}
                    </span>
                  </div>
                  <div className="flex justify-end text-[14px] text-gray-600">
                    <span>
                      {isLoadingExchangeRate ? (
                        <Skeleton className="h-4 w-[150px]" />
                      ) : (
                         formatCurrency(total, 'GBP')
                      )}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded-md mb-4">
                <div className="flex items-start mb-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-5 h-5 text-blue-600 mr-2 flex-shrink-0 mt-0.5"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"
                    />
                  </svg>
                  <div className="text-sm text-gray-700">
                    Shipping costs are calculated based on your delivery
                    location ({userAddress?.DeliverTo || "United Kingdom"}).
                  </div>
                </div>
                <div className="flex items-start mb-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-5 h-5 text-blue-600 mr-2 flex-shrink-0 mt-0.5"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M8.25 18.75a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 01-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 00-3.213-9.193 2.056 2.056 0 00-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 00-10.026 0 1.106 1.106 0 00-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
                    />
                  </svg>
                  <div className="text-sm text-gray-700">
                    Items from different origins will ship separately and may
                    arrive at different times.
                  </div>
                </div>
                <div className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-5 h-5 text-blue-600 mr-2 flex-shrink-0 mt-0.5"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z"
                    />
                  </svg>
                  <div className="text-sm text-gray-700">
                    All prices are shown in GBP and converted to{" "}
                    {userCurrencyCode} where necessary.
                  </div>
                </div>
                <div className="flex items-start mt-2">
                  <TriangleAlert className="h-5 w-5 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-yellow-700">
                    Please ensure your address is accurate at the time of
                    placing your order. Any additional delivery costs or issues
                    arising from incorrect or incomplete address details will be
                    the responsibility of the customer.
                  </div>
                </div>
              </div>

              <button
                className={`w-full py-3 text-white rounded-md font-medium ${
                  isLoadingUser || !userProfile || areAllAddressFlagsNull()
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-blue-600 hover:bg-blue-700"
                }`}
                onClick={handleProceedToPayment}
                disabled={
                  isLoadingUser || !userProfile || areAllAddressFlagsNull()
                }
              >
                {isLoadingUser
                  ? "Loading..."
                  : areAllAddressFlagsNull()
                    ? "Update Address to Continue"
                    : "Proceed to Payment"}
              </button>

              <div className="mt-4 text-xs text-gray-600">
                <p>
                  By proceeding, you agree to our Terms of Service and Privacy
                  Policy.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stripe Payment Modal */}
      {cartItems.length > 0 && userProfile && (
        <StripePaymentOrder
          isOpen={isPaymentModalOpen}
          onClose={handleClosePaymentModal}
          onSuccess={handlePaymentSuccess}
          userId={parseInt(userProfile.id || "0")}
          email={userProfile.email}
          items={calculatePaymentTotals().orderItems}
          subtotal={calculatePaymentTotals().subtotal}
          taxAmount={0}
          shippingCost={calculatePaymentTotals().shippingCost}
          totalAmount={calculatePaymentTotals().totalAmount}
          currency={calculatePaymentTotals().currency}
        />
      )}
    </div>
  );
};

export default CheckoutPage;
