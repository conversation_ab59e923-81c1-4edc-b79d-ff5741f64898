"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import {
  Calendar,
  Package,
  Truck,
  CheckCircle,
  Clock,
  MapPin,
  Download,
  X,
  Eye,
} from "lucide-react";
import { Order, OrderStatus } from "@/data/models/order.model";
import { OrderService } from "@/services/order.service";
import { UserService } from "@/services/user.service";
import { AddressService } from "@/services/address.service";
import { useCurrency } from "@/components/app/CurrencyProvider";
import {
  formatCurrencyWithDigits,
  formatCurrencyFallback,
} from "@/lib/currency";
import { Skeleton } from "@/components/ui/skeleton";
import { country_currencies } from "@/lib/constants";
import ReactMarkdown from "react-markdown";
import { useAuth } from "@/hooks/use-auth.ts";

const statusConfig: Record<
  OrderStatus,
  { color: string; icon: React.ReactElement; label: string }
> = {
  pending: {
    color: "bg-gray-100 text-gray-800",
    icon: <Clock className="w-4 h-4" />,
    label: "Pending",
  },
  confirmed: {
    color: "bg-blue-100 text-blue-800",
    icon: <CheckCircle className="w-4 h-4" />,
    label: "Confirmed",
  },
  processing: {
    color: "bg-yellow-100 text-yellow-800",
    icon: <Clock className="w-4 h-4" />,
    label: "Processing",
  },
  shipped: {
    color: "bg-blue-100 text-blue-800",
    icon: <Truck className="w-4 h-4" />,
    label: "Shipped",
  },
  delivered: {
    color: "bg-green-100 text-green-800",
    icon: <CheckCircle className="w-4 h-4" />,
    label: "Delivered",
  },
  cancelled: {
    color: "bg-red-100 text-red-800",
    icon: <Clock className="w-4 h-4" />,
    label: "Cancelled",
  },
};

const OrderCard: React.FC<{
  order: Order;
  onCancelOrder?: () => Promise<boolean>;
  exchangeRate: number | null;
  userCurrencyCode: string;
}> = ({ order, onCancelOrder, exchangeRate, userCurrencyCode }) => {
  const [isCancelling, setIsCancelling] = useState(false);
  const status = statusConfig[order.status];

  // Currency context for display/loading state
  const { isLoadingExchangeRate } = useCurrency();

  // Helper to truncate description text
  const truncateDescription = (
    text: string,
    maxLength: number = 100,
  ): string => {
    if (!text) return "";
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + "...";
  };

  // Helper to convert using provided exchangeRate from parent
  const convertAmount = (amount: number): number => {
    if (userCurrencyCode === "GBP" || exchangeRate === null) return amount;
    return amount * exchangeRate;
  };

  // Helper to format currency symbol for user currency (centralized helper, preserve 0 decimals)
  // This rounds to 0 digits to preserve the original visual behaviour of the page.
  const formatCurrency = (amount: number): string => {
    try {
      // Round and format using the centralized helper with 0 fraction digits.
      return formatCurrencyWithDigits(amount, userCurrencyCode || "GBP", 0);
    } catch {
      // Centralized fallback that mimics the previous minimal representation.
      return formatCurrencyFallback(amount, userCurrencyCode || "GBP");
    }
  };

  // Check if cancel button should be disabled
  const isCancelDisabled = () => {
    // Disable for certain statuses
    const disabledStatuses: OrderStatus[] = [
      "processing",
      "confirmed",
      "shipped",
      "delivered",
      "cancelled",
    ];
    if (disabledStatuses.includes(order.status)) {
      return true;
    }

    // Disable if order is older than 7 days
    const orderDate = new Date(order.created_at);
    const currentDate = new Date();
    const daysDiff = Math.floor(
      (currentDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24),
    );

    if (daysDiff > 7) {
      return true;
    }

    // Disable if any item in the order is not refundable
    const hasNonRefundableItems = order.items.some(
      (item) => !item.product_snapshot.refundable,
    );
    if (hasNonRefundableItems) {
      return true;
    }

    return false;
  };

  const handleCancelOrder = async () => {
    if (!onCancelOrder || isCancelling) return;

    setIsCancelling(true);
    try {
      const success = await onCancelOrder();
      if (!success) {
        console.error("Failed to cancel order");
      }
    } catch (error) {
      console.error("Failed to cancel order:", error);
    } finally {
      setIsCancelling(false);
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
      {/* Order Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Order {order.order_number}
            </h3>
            <span
              className={`inline-flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-medium ${status.color} w-fit`}
            >
              {status.icon}
              {status.label}
            </span>
          </div>
          <div className="flex flex-col sm:items-end text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              Ordered: {new Date(order.created_at).toLocaleDateString("en-GB")}
            </div>
            {order.delivered_at && (
              <div className="text-green-600 mt-1">
                Delivered:{" "}
                {new Date(order.delivered_at).toLocaleDateString("en-GB")}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Order Items */}
      <div className="p-6">
        <div className="space-y-4">
          {order.items.map((item, index) => (
            <div key={index} className="flex gap-4">
              <div className="relative w-16 h-16 sm:w-20 sm:h-20 flex-shrink-0">
                {item.variant_snapshot?.image?.url ? (
                  <Image
                    src={item.variant_snapshot.image.url}
                    alt={item.product_snapshot.title}
                    width={80}
                    height={80}
                    className="w-full h-full object-contain rounded-md"
                    onError={(e) => {
                      // Fallback to Package icon if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = "none";
                      target.nextElementSibling?.classList.remove("hidden");
                    }}
                  />
                ) : null}
                <div
                  className={`w-full h-full bg-gray-200 rounded-md flex items-center justify-center ${
                    item.variant_snapshot?.image?.url ? "hidden" : ""
                  }`}
                >
                  <Package className="w-8 h-8 text-gray-400" />
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex flex-col sm:flex-row sm:justify-between gap-2">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 truncate">
                      {item.product_snapshot.title}
                    </h4>
                    <ReactMarkdown>
                      {truncateDescription(
                        item.product_snapshot.description,
                        120,
                      )}
                    </ReactMarkdown>

                    {/* Variant Options as Tag Pills */}
                    {item.variant_snapshot?.option_values &&
                      Object.keys(item.variant_snapshot.option_values).length >
                        0 && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {Object.entries(
                            item.variant_snapshot.option_values,
                          ).map(([key, value]) => (
                            <span
                              key={key}
                              className="inline-block bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full"
                            >
                              {key}: {value}
                            </span>
                          ))}
                        </div>
                      )}
                  </div>
                  <div className="text-right">
                    <div className="flex flex-col sm:items-end">
                      <span className="text-sm text-gray-600">
                        Qty: {item.quantity}
                      </span>
                      <span className="font-medium">
                        {isLoadingExchangeRate ? (
                          <Skeleton className="w-16 h-4" />
                        ) : exchangeRate !== null ? (
                          formatCurrency(convertAmount(item.unit_price))
                        ) : (
                          formatCurrencyWithDigits(
                            item.unit_price,
                            item.currency || "GBP",
                            2,
                          )
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Order Summary & Actions */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
        <div className="flex flex-col lg:flex-row lg:justify-between gap-4">
          {/* Shipping Info */}
          {order.shipping_details ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Truck className="w-4 h-4" />
                <span>
                  {order.shipping_details?.partner_name || "Standard Shipping"}
                </span>
                {order.shipping_details?.tracking_id && (
                  <span className="font-mono text-xs bg-white px-2 py-1 rounded border">
                    {order.shipping_details?.tracking_id}
                  </span>
                )}
              </div>
              <div className="flex items-start gap-2 text-sm text-gray-600">
                <MapPin className="w-4 h-4 mt-0.5 flex-shrink-0" />
                <div>
                  <div className="font-medium">
                    {order.shipping_address.recipient_name}
                  </div>
                  <div>
                    {order.shipping_address.address_line_1}
                    {order.shipping_address.address_line_2 &&
                      `, ${order.shipping_address.address_line_2}`}
                    <br />
                    {order.shipping_address.city},{" "}
                    {order.shipping_address.postal_code},{" "}
                    {order.shipping_address.country}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex items-start gap-2 text-sm text-gray-600">
                <MapPin className="w-4 h-4 mt-0.5 flex-shrink-0" />
                <div>
                  <div className="font-medium">
                    {order.shipping_address.recipient_name}
                  </div>
                  <div>
                    {order.shipping_address.address_line_1}
                    {order.shipping_address.address_line_2 &&
                      `, ${order.shipping_address.address_line_2}`}
                    <br />
                    {order.shipping_address.city},{" "}
                    {order.shipping_address.postal_code},{" "}
                    {order.shipping_address.country}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Total & Actions */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <div className="text-right">
              <div className="text-sm text-gray-600">Order Total</div>
              <div className="text-lg font-semibold text-gray-900">
                {isLoadingExchangeRate ? (
                  <Skeleton className="w-36 h-5" />
                ) : exchangeRate !== null ? (
                  formatCurrency(convertAmount(order.total_amount))
                ) : (
                  formatCurrencyWithDigits(
                    order.total_amount,
                    order.currency || "GBP",
                    2,
                  )
                )}
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              {order.payment_details.invoice_url ? (
                <a
                  href={order.payment_details.invoice_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-3 py-2 text-sm border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors flex items-center gap-1"
                >
                  <Eye className="w-4 h-4" />
                  View Receipt
                </a>
              ) : (
                <button className="px-3 py-2 text-sm border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors flex items-center gap-1">
                  <Download className="w-4 h-4" />
                  Invoice
                </button>
              )}

              <button
                onClick={handleCancelOrder}
                disabled={isCancelDisabled() || isCancelling}
                className={`px-3 py-2 text-sm border rounded-md transition-colors flex items-center gap-1 ${
                  isCancelDisabled() || isCancelling
                    ? "border-gray-200 text-gray-400 cursor-not-allowed"
                    : "border-red-300 text-red-700 hover:bg-red-50"
                }`}
              >
                <X className="w-4 h-4" />
                {isCancelling ? "Cancelling..." : "Cancel Order"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function MyOrdersPage() {
  // Use auth hook to guard user-only operations
  const {
    isAuthenticated,
    redirectIfNotAuthenticated,
    loading: authLoading,
    user
  } = useAuth();

  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);
  const [statusFilter, setStatusFilter] = useState<OrderStatus | undefined>(
    undefined,
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const {
    currencyCode: userCurrencyCode,
    exchangeRate,
    isLoadingExchangeRate,
  } = useCurrency();

  const orderService = new OrderService();
  const addressService = new AddressService();

  const ordersPerPage = 5;

  // Helper: get currency code by country name
  const getCurrencyCodeByCountryName = (countryName: string): string => {
    const currencyCode = Object.keys(country_currencies).find(
      (code) =>
        country_currencies[code as keyof typeof country_currencies] ===
        countryName,
    );
    return currencyCode || "GBP";
  };

  // Load user address (no local currency mutation; provider will handle any preference)
  useEffect(() => {
    const loadUserAddress = async () => {
      try {
        await addressService.fetchUserAddress();
        // CurrencyProvider performs bootstrapping and will apply address-based preferences where appropriate.
      } catch (e) {
        // informational only; do not mutate local currency here
        // console.warn("Failed to load user address (info only):", e);
      }
    };
    loadUserAddress();
  }, []);

  // Exchange rates and currency selection are handled by CurrencyProvider.
  // This component consumes `useCurrency()` for `currencyCode` and `exchangeRate`
  // and therefore does not perform its own exchange-rate fetching here.

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  useEffect(() => {
    fetchOrders();
  }, [user, currentPage, statusFilter, debouncedSearchTerm]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(null);

      // Ensure auth state before attempting to fetch profile
      if (!authLoading && !isAuthenticated) {
        // Not authenticated — redirect to login and preserve return path
        redirectIfNotAuthenticated();
        return;
      }

      // Fetch orders for the current user with filters
      const result = await orderService.listOrders(
        statusFilter,
        debouncedSearchTerm || undefined,
        currentPage,
        ordersPerPage,
        user?.id,
      );

      setOrders(result.orders);
      setTotalOrders(result.total);
    } catch (err) {
      console.error("Error fetching orders:", err);
      setError("Failed to load orders. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const totalPages = Math.ceil(totalOrders / ordersPerPage);

  const handleStatusFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    const value = e.target.value;
    setStatusFilter(value === "all" ? undefined : (value as OrderStatus));
    setCurrentPage(1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCancelOrder = async (orderId: string): Promise<boolean> => {
    try {
      const success = await orderService.cancelOrder(orderId);
      if (success) {
        // Refresh the orders list after successful cancellation
        await fetchOrders();
      }
      return success;
    } catch (error) {
      console.error("Error cancelling order:", error);
      return false;
    }
  };

  const clearFilters = () => {
    setStatusFilter(undefined);
    setSearchTerm("");
    setDebouncedSearchTerm("");
    setCurrentPage(1);
  };

  const hasActiveFilters =
    statusFilter !== undefined || debouncedSearchTerm.trim() !== "";

  if (loading) {
    return (
      <div className="bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">My Orders</h1>
            <p className="text-gray-600">Track and manage your order history</p>
          </div>
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">My Orders</h1>
            <p className="text-gray-600">Track and manage your order history</p>
          </div>
          <div className="text-center py-12">
            <div className="text-red-600 mb-4">{error}</div>
            <button
              onClick={fetchOrders}
              className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Orders</h1>
          <p className="text-gray-600">Track and manage your order history</p>
        </div>

        {/* Filters */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <select
            value={statusFilter || "all"}
            onChange={handleStatusFilterChange}
            className="px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Orders</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="processing">Processing</option>
            <option value="shipped">Shipped</option>
            <option value="delivered">Delivered</option>
            <option value="cancelled">Cancelled</option>
          </select>
          <div className="flex-1"></div>
          <input
            type="search"
            placeholder="Search orders..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Orders List */}
        {orders.length > 0 ? (
          <>
            <div className="space-y-6">
              {orders.map((order) => (
                <OrderCard
                  key={order.id}
                  order={order}
                  onCancelOrder={() => handleCancelOrder(order.id)}
                  exchangeRate={exchangeRate}
                  userCurrencyCode={userCurrencyCode}
                />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-8 flex justify-center">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="px-3 py-2 text-sm border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`px-3 py-2 text-sm rounded-md transition-colors ${
                          currentPage === page
                            ? "bg-blue-600 text-white"
                            : "border border-gray-300 text-gray-700 hover:bg-gray-50"
                        }`}
                      >
                        {page}
                      </button>
                    );
                  })}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="px-3 py-2 text-sm border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        ) : (
          /* Empty State */
          <div className="text-center py-12">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            {hasActiveFilters ? (
              <>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No orders found
                </h3>
                <p className="text-gray-600 mb-6">
                  No orders match your current filters. Try adjusting your
                  search or status filter.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <button
                    onClick={clearFilters}
                    className="px-6 py-3 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                  >
                    Clear Filters
                  </button>
                  <button className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    Start Shopping
                  </button>
                </div>
              </>
            ) : (
              <>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No orders yet
                </h3>
                <p className="text-gray-600 mb-6">
                  When you place your first order, it will appear here.
                </p>
                <button className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                  Start Shopping
                </button>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
