"use client";

import React, { useState, useEffect, useMemo, Suspense } from "react";
import Image from "next/image";
import { useRouter } from "next/router";
import { ChevronDown, Filter, X } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { ProductService } from "@/services/product.service";
import {
  Product,
  ExternalProductDetails,
  InternalProductDetails,
} from "@/data/models/product.model";
import { useCartDispatch, addToCart } from "@/lib/CartContext";
import { categories, country_currencies } from "@/lib/constants";
import { AddressService, UserAddressModel } from "@/services/address.service";
import { ExchangeRateService } from "@/services/exchange.service";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import Link from "next/link";

// Helper function for currency formatting
const formatCurrency = (amount: number, fromCurrency: string) => {
  let symbol = "£";
  switch (fromCurrency) {
    case "GHS":
      symbol = "₵";
      break;
    case "NGN":
      symbol = "₦";
      break;
    default: // GBP and any other case
      symbol = "£";
  }
  return `${symbol}${amount.toLocaleString("en-GB", {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })}`;
};

const ProductListingContent: React.FC = () => {
  const searchParams = useSearchParams();
  const dispatch = useCartDispatch();
  // const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [userCurrencyCode, setUserCurrencyCode] = useState<string>("GBP");
  const [exchangeRate, setExchangeRate] = useState<number | null>(null);
  const [isLoadingExchangeRate, setIsLoadingExchangeRate] = useState(false);

  // Service instances
  const addressService = new AddressService();
  const exchangeRateService = new ExchangeRateService();

  // Helper function to get display pricing (considering variants)
  const getDisplayPricing = (product: Product) => {
    // If product has variants, use the first variant's pricing as display price
    if (product.variants?.variants && product.variants.variants.length > 0) {
      const firstVariant = product.variants.variants[0];
      return {
        price: firstVariant.price || product.primary_data.price,
        sale_price: firstVariant.sale_price || product.primary_data.sale_price,
        hasVariants: true,
      };
    }

    // For products without variants, use primary_data
    return {
      price: product.primary_data.price,
      sale_price: product.primary_data.sale_price,
      hasVariants: false,
    };
  };

  // Helper function to convert price using stored exchange rate
  const convertPrice = (price: number) => {
    if (userCurrencyCode === "GBP" || !exchangeRate) {
      return price;
    }
    return price * exchangeRate;
  };

  // Fetch exchange rate once and store it
  const fetchExchangeRate = async () => {
    if (userCurrencyCode === "GBP") {
      setExchangeRate(1);
      return;
    }

    setIsLoadingExchangeRate(true);
    try {
      const exchangeData =
        await exchangeRateService.getExchangeRateISO(userCurrencyCode);
      if (exchangeData && exchangeData.rate_per_gbp) {
        setExchangeRate(exchangeData.rate_per_gbp);
      } else {
        setExchangeRate(1); // Fallback to 1:1 rate
      }
    } catch (error) {
      console.error("Failed to fetch exchange rate:", error);
      setExchangeRate(1); // Fallback to 1:1 rate
    } finally {
      setIsLoadingExchangeRate(false);
    }
  };

  // Helper function to get currency code by country name
  const getCurrencyCodeByCountryName = (countryName: string): string => {
    const currencyCode = Object.keys(country_currencies).find(
      (code) =>
        country_currencies[code as keyof typeof country_currencies] ===
        countryName,
    );
    return currencyCode || "GBP"; // Default to GBP if country not found
  };

  // Load user address and set currency code
  useEffect(() => {
    const loadUserAddress = async () => {
      try {
        const address = await addressService.fetchUserAddress();

        // Get currency code from country
        if (address.DeliverTo) {
          const currencyCode = getCurrencyCodeByCountryName(address.DeliverTo);
          setUserCurrencyCode(currencyCode);
        }
      } catch (error) {
        console.error("Failed to load user address:", error);
        setUserCurrencyCode("GBP");
      }
    };

    loadUserAddress();
  }, []);

  // Fetch exchange rate when currency changes
  useEffect(() => {
    fetchExchangeRate();
  }, [userCurrencyCode]);
  const [sortBy, setSortBy] = useState("Price: Low to High");
  const [showFilters, setShowFilters] = useState(false);
  const [minPrice, setMinPrice] = useState("");
  const [maxPrice, setMaxPrice] = useState("");
  const [selectedCondition, setSelectedCondition] = useState<string[]>([]);
  const [selectedOrigin, setSelectedOrigin] = useState<string[]>([]);
  const [selectedShippingLocation, setSelectedShippingLocation] = useState<
    string[]
  >([]);
  const [selectedType, setSelectedType] = useState<string[]>([]);

  // Extract search query from URL parameters and execute search
  useEffect(() => {
    const query = searchParams.get("query") || "";
    setSearchQuery(query);

    if (query.trim()) {
      const executeSearch = async () => {
        setIsLoading(true);
        try {
          const productService = new ProductService();
          const searchResults = await productService.searchProductsByName(
            query.trim(),
          );
          setProducts(searchResults);
        } catch (error) {
          console.error("Error searching products:", error);
          setProducts([]);
        } finally {
          setIsLoading(false);
        }
      };

      executeSearch();
    } else {
      setProducts([]);
      setIsLoading(false);
    }
  }, [searchParams, userCurrencyCode]);

  const [currentPage, setCurrentPage] = useState(1);
  const resultsPerPage = 10;

  // Filter and sort products
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products.filter((product) => {
      // Get variant-aware pricing
      const pricing = getDisplayPricing(product);
      const convertedPrice = convertPrice(pricing.sale_price || pricing.price);

      // Price filter (using converted prices)
      if (minPrice && convertedPrice < parseFloat(minPrice)) return false;
      if (maxPrice && convertedPrice > parseFloat(maxPrice)) return false;

      // Condition filter
      if (
        selectedCondition.length &&
        !selectedCondition.includes(product.condition)
      )
        return false;

      // Origin filter
      if (
        selectedOrigin.length &&
        !selectedOrigin.includes(product.origin_location)
      )
        return false;

      // Shipping location filter
      if (selectedShippingLocation.length) {
        if (!product.shipping_rates || product.shipping_rates.length === 0)
          return false;
        const hasValidShipping = product.shipping_rates.some((rate) =>
          selectedShippingLocation.includes(rate.to_country),
        );
        if (!hasValidShipping) return false;
      }

      // Type filter
      if (selectedType.length) {
        const productType =
          product.type === "internal" ? "Internal" : "External";
        if (!selectedType.includes(productType)) return false;
      }

      return true;
    });

    // Sort filtered products
    return [...filtered].sort((a, b) => {
      const pricingA = getDisplayPricing(a);
      const pricingB = getDisplayPricing(b);
      const priceA = convertPrice(pricingA.sale_price || pricingA.price);
      const priceB = convertPrice(pricingB.sale_price || pricingB.price);
      return sortBy === "Price: Low to High"
        ? priceA - priceB
        : priceB - priceA;
    });
  }, [
    products,
    minPrice,
    maxPrice,
    selectedCondition,
    selectedOrigin,
    selectedShippingLocation,
    selectedType,
    sortBy,
    exchangeRate,
    userCurrencyCode,
  ]);

  const totalResults = filteredAndSortedProducts.length;
  const totalPages = Math.ceil(totalResults / resultsPerPage);
  const showPagination = totalResults > resultsPerPage;

  // Get current page products
  const currentProducts = useMemo(() => {
    const startIndex = (currentPage - 1) * resultsPerPage;
    const endIndex = startIndex + resultsPerPage;
    return filteredAndSortedProducts.slice(startIndex, endIndex);
  }, [filteredAndSortedProducts, currentPage, resultsPerPage]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [
    minPrice,
    maxPrice,
    selectedCondition,
    selectedOrigin,
    selectedShippingLocation,
    selectedType,
    sortBy,
  ]);

  const handleConditionChange = (condition: string) => {
    setSelectedCondition((prev) =>
      prev.includes(condition)
        ? prev.filter((c) => c !== condition)
        : [...prev, condition],
    );
  };

  const handleOriginChange = (origin: string) => {
    setSelectedOrigin((prev) =>
      prev.includes(origin)
        ? prev.filter((o) => o !== origin)
        : [...prev, origin],
    );
  };

  const handleShippingLocationChange = (location: string) => {
    setSelectedShippingLocation((prev) =>
      prev.includes(location)
        ? prev.filter((l) => l !== location)
        : [...prev, location],
    );
  };

  const handleTypeChange = (type: string) => {
    setSelectedType((prev) =>
      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type],
    );
  };

  const isProductOutOfStock = (product: Product): boolean => {
    if (product.type === "internal") {
      // For products with variants, check if any variant has stock
      if (product.variants?.variants && product.variants.variants.length > 0) {
        return product.variants.variants.every(
          (variant) => (variant.stock || 0) === 0,
        );
      }
      // For products without variants, stock information is not available at product level
      // so we assume they're in stock unless explicitly marked otherwise
      return false;
    }
    return false;
  };

  const getButtonText = (product: Product) => {
    if (isProductOutOfStock(product)) {
      return "Out of Stock";
    }
    // If product has 0 or 1 variant, show "Add to cart"
    if (
      product.type === "internal" &&
      (!product.variants?.variants || product.variants.variants.length <= 1)
    ) {
      return "Add to cart";
    }
    if (product.type === "external" || product.type === "internal") {
      return "View";
    }
  };

  const getButtonStyle = (product: Product) => {
    if (isProductOutOfStock(product)) {
      return "bg-gray-400 cursor-not-allowed";
    }
    return product.type === "internal"
      ? "bg-blue-600 hover:bg-blue-700"
      : "bg-blue-600 hover:bg-blue-700";
  };

  // const handleAddToCart = (product: Product) => {
  //   if (product.type === 'internal') {
  //     addToCart(dispatch, product);
  //   }
  // };

  const handleProductAction = (product: Product) => {
    // Prevent action if product is out of stock
    if (isProductOutOfStock(product)) {
      return;
    }

    if (product.type === "external") {
      // For external products, open the external URL
      const externalDetails = product.details as ExternalProductDetails;
      window.open(externalDetails.origin_url, "_blank");
    } else if (
      // For internal products with 0 or 1 variant, add to cart directly
      !product.variants?.variants ||
      product.variants.variants.length <= 1
    ) {
      addToCart(dispatch, product);
    } else {
      // For internal products with multiple variants, go to product page
      window.open(`/home/<USER>/products/${product.id}`, "_self");
    }
  };

  // Helper functions to resolve category IDs to display names
  const getCategoryName = (categoryId: string): string => {
    const category = categories.find(
      (cat) => cat.id === categoryId && cat.parent_id === null,
    );
    return category ? category.name : categoryId;
  };

  const getSubcategoryName = (subcategoryId: string): string => {
    const subcategory = categories.find(
      (cat) => cat.id === subcategoryId && cat.parent_id !== null,
    );
    return subcategory ? subcategory.name : subcategoryId;
  };

  const getCategoryHierarchy = (
    categoryId: string,
    subcategoryId: string,
  ): string => {
    const categoryName = getCategoryName(categoryId);
    const subcategoryName = getSubcategoryName(subcategoryId);
    return `${categoryName} › ${subcategoryName}`;
  };

  return (
    <div className="bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <div className="text-sm text-gray-600">
            {searchQuery && (
              <div className="mb-2">
                Search results for:{" "}
                <span className="font-semibold">"{searchQuery}"</span>
              </div>
            )}
            Showing 1—{Math.min(resultsPerPage, totalResults)} of {totalResults}{" "}
            results
          </div>

          <div className="flex items-center justify-between sm:justify-end gap-4 w-full sm:w-auto">
            {/* Mobile Filter Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="lg:hidden flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg bg-white"
            >
              <Filter size={16} />
              Filters
            </button>

            {/* Sort Dropdown */}
            <div className="flex items-center gap-2">
              <span className="hidden sm:block text-sm text-gray-600">
                Sort by:
              </span>
              <div className="relative">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option>Price: Low to High</option>
                  <option>Price: High to Low</option>
                </select>
                <ChevronDown
                  size={16}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-6 relative">
          {/* Filters Sidebar */}
          <div
            className={`${showFilters ? "block" : "hidden"} lg:block fixed lg:relative inset-0 lg:inset-auto z-50 lg:z-auto`}
          >
            <div
              className="lg:hidden fixed inset-0 bg-black bg-opacity-50"
              onClick={() => setShowFilters(false)}
            />
            <div className="fixed lg:relative left-0 top-0 h-full lg:h-auto w-80 lg:w-64 bg-white lg:bg-transparent p-6 lg:p-0 overflow-y-auto">
              {/* Mobile close button */}
              <button
                onClick={() => setShowFilters(false)}
                className="lg:hidden absolute top-4 right-4 p-2"
              >
                <X size={20} />
              </button>

              <div className="space-y-6 mt-8 lg:mt-0">
                {/* Condition Filter */}
                <div className="bg-white rounded-lg p-4 shadow-sm border">
                  <h3 className="font-medium text-gray-900 mb-3">Condition</h3>
                  <div className="space-y-3">
                    {["New", "Used"].map((condition) => (
                      <div
                        key={condition}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          id={`condition-${condition}`}
                          checked={selectedCondition.includes(condition)}
                          onCheckedChange={() =>
                            handleConditionChange(condition)
                          }
                        />
                        <Label
                          htmlFor={`condition-${condition}`}
                          className="text-sm text-gray-700 cursor-pointer select-none"
                        >
                          {condition}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Origin Filter */}
                <div className="bg-white rounded-lg p-4 shadow-sm border">
                  <h3 className="font-medium text-gray-900 mb-3">Origin</h3>
                  <div className="space-y-3">
                    {["UK", "Ghana"].map((origin) => (
                      // <label key={origin} className="flex items-center cursor-pointer min-h-[44px] relative group">
                      //   <input
                      //     type="checkbox"
                      //     checked={selectedOrigin.includes(origin)}
                      //     onChange={() => handleOriginChange(origin)}
                      //     className="peer w-5 h-5 lg:w-4 lg:h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2 relative z-10"
                      //   />
                      //   <span className="absolute inset-0 -z-1" aria-hidden="true" />
                      //   <span className="ml-3 text-sm text-gray-700 relative z-10">{origin}</span>
                      // </label>
                      <div key={origin} className="flex items-center space-x-2">
                        <Checkbox
                          id={`origin-${origin}`}
                          checked={selectedOrigin.includes(origin)}
                          onCheckedChange={() => handleOriginChange(origin)}
                        />
                        <Label
                          htmlFor={`origin-${origin}`}
                          className="text-sm text-gray-700 cursor-pointer select-none"
                        >
                          {origin}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Price Filter */}
                <div className="bg-white rounded-lg p-4 shadow-sm border">
                  <h3 className="font-medium text-gray-900 mb-3">Price</h3>
                  <div className="flex items-center gap-2">
                    <input
                      type="number"
                      placeholder="Min"
                      value={minPrice}
                      onChange={(e) => setMinPrice(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <span className="text-gray-500">—</span>
                    <input
                      type="number"
                      placeholder="Max"
                      value={maxPrice}
                      onChange={(e) => setMaxPrice(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                {/* Shipping Location Filter */}
                <div className="bg-white rounded-lg p-4 shadow-sm border">
                  <h3 className="font-medium text-gray-900 mb-3">
                    Shipping Location
                  </h3>
                  <div className="space-y-3">
                    {["UK", "Ghana", "Nigeria"].map((location) => (
                      <div
                        key={location}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          id={`location-${location}`}
                          checked={selectedShippingLocation.includes(location)}
                          onCheckedChange={() =>
                            handleShippingLocationChange(location)
                          }
                        />
                        <Label
                          htmlFor={`location-${location}`}
                          className="text-sm text-gray-700 cursor-pointer select-none"
                        >
                          {location}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Type Filter */}
                <div className="bg-white rounded-lg p-4 shadow-sm border">
                  <h3 className="font-medium text-gray-900 mb-3">Type</h3>
                  <div className="space-y-3">
                    {["Internal", "External"].map((type) => (
                      <div key={type} className="flex items-center space-x-2">
                        <Checkbox
                          id={`type-${type}`}
                          checked={selectedType.includes(type)}
                          onCheckedChange={() => handleTypeChange(type)}
                        />
                        <Label
                          htmlFor={`type-${type}`}
                          className="text-sm text-gray-700 cursor-pointer select-none"
                        >
                          {type}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1">
            {isLoading ? (
              <div className="text-center py-12">
                <div className="text-gray-500 text-lg mb-2">
                  Searching products...
                </div>
                <div className="text-gray-400 text-sm">
                  Please wait while we find products for you
                </div>
              </div>
            ) : totalResults === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-500 text-lg mb-2">
                  No products found
                </div>
                {searchQuery ? (
                  <div className="text-gray-400 text-sm">
                    <p>
                      Can't find what you're looking for? Click on the link
                      below to drop a recommendation.
                    </p>
                    <Link
                      href={
                        "https://docs.google.com/forms/d/e/1FAIpQLSefeL_sb6SV6TQUGk3qbB-zgSDx1aakGTgiQu4DNBUZ4qYB1w/viewform?usp=sharing&ouid=113597109975813091882"
                      }
                      className="underline text-blue-500"
                    >
                      Drop a recommendation
                    </Link>
                  </div>
                ) : (
                  <div className="text-gray-400 text-sm">
                    Try adjusting your filters to see more results
                  </div>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {currentProducts.map((product) => {
                  // Get variant-aware pricing
                  const pricing = getDisplayPricing(product);

                  return (
                    <div
                      key={product.id}
                      className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow flex flex-col h-full"
                    >
                      {/* Product Badge */}
                      <div className="relative">
                        <div className="absolute top-3 left-3 bg-gray-100 px-2 py-1 rounded text-xs font-medium text-gray-700 z-10">
                          {product.condition}
                        </div>

                        {/* Product Image */}
                        <div className="aspect-[4/3] bg-gray-100 rounded-t-lg overflow-hidden">
                          {(() => {
                            const imageUrl =
                              product.variants?.variants?.[0]?.images?.[0]
                                ?.url || product.primary_image?.url;
                            return imageUrl ? (
                              <Image
                                src={imageUrl}
                                alt={product.title}
                                width={300}
                                height={200}
                                className="w-full h-full object-contain object-center transform group-hover:scale-105 transition-transform duration-500"
                                onError={(e) => {
                                  // Fallback to placeholder if image fails to load
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = "none";
                                  target.nextElementSibling?.classList.remove(
                                    "hidden",
                                  );
                                }}
                              />
                            ) : null;
                          })()}
                          <div
                            className={`w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center ${
                              product.variants?.variants?.[0]?.images?.[0]
                                ?.url || product.primary_image?.url
                                ? "hidden"
                                : ""
                            }`}
                          >
                            <span className="text-gray-500 text-sm">
                              No Image Available
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Product Info */}
                      <div className="p-4 flex flex-col flex-grow">
                        <div className="flex-grow">
                          <h3 className="font-medium text-gray-900 mb-1">
                            {product.title}
                          </h3>
                          <p className="text-sm text-gray-600 mb-2">
                            {getCategoryHierarchy(
                              product.category,
                              product.subcategory,
                            )}
                          </p>

                          {/* Price */}
                          <div className="flex items-center gap-2 mb-2">
                            {pricing.sale_price &&
                            pricing.sale_price < pricing.price ? (
                              <>
                                <span className="text-lg font-semibold text-gray-900">
                                  {formatCurrency(
                                    convertPrice(pricing.sale_price),
                                    userCurrencyCode,
                                  )}
                                </span>
                                <span className="text-sm text-gray-500 line-through">
                                  {formatCurrency(
                                    convertPrice(pricing.price),
                                    userCurrencyCode,
                                  )}
                                </span>
                                <span className="text-sm text-green-600 font-medium">
                                  Save{" "}
                                  {Math.round(
                                    ((pricing.price - pricing.sale_price) /
                                      pricing.price) *
                                      100,
                                  )}
                                  %
                                </span>
                              </>
                            ) : (
                              <span className="text-lg font-semibold text-gray-900">
                                {formatCurrency(
                                  convertPrice(pricing.price),
                                  userCurrencyCode,
                                )}
                              </span>
                            )}
                          </div>

                          {/* Shipping Info */}
                          <div className="text-sm text-gray-600 mb-1">
                            Ships from: {product.origin_location}
                          </div>
                          <div className="text-sm text-gray-600 mb-4">
                            Delivery in{" "}
                            {product.shipping_rates?.[0]
                              ?.estimated_delivery_days || "N/A"}{" "}
                            days
                          </div>

                          {/* External Product Info */}
                          {product.type === "external" && (
                            <div className="mb-4">
                              <div className="text-sm text-gray-600 mb-1">
                                Sold via{" "}
                                {
                                  (product.details as ExternalProductDetails)
                                    .origin_name
                                }
                              </div>
                              {(product.details as ExternalProductDetails)
                                .discount_code && (
                                <div className="text-sm text-blue-600">
                                  Use code{" "}
                                  {
                                    (product.details as ExternalProductDetails)
                                      .discount_code
                                  }
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        {/* Action Button - Always at bottom */}
                        <button
                          onClick={() => handleProductAction(product)}
                          disabled={isProductOutOfStock(product)}
                          className={`w-full py-2 px-4 rounded-lg text-white font-medium transition-colors mt-auto ${getButtonStyle(product)}`}
                        >
                          {getButtonText(product)}
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Pagination - Only show when results > 10 */}
            {showPagination && (
              <>
                <div className="flex items-center justify-center gap-2 mt-8">
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={currentPage === 1}
                    className={`px-3 py-2 text-sm rounded ${
                      currentPage === 1
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                    }`}
                  >
                    Prev
                  </button>

                  {/* Page Numbers */}
                  {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-2 text-sm rounded ${
                          currentPage === pageNum
                            ? "bg-blue-600 text-white"
                            : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  {totalPages > 5 && currentPage < totalPages - 2 && (
                    <>
                      <span className="px-2 text-gray-500">...</span>
                      <button
                        onClick={() => setCurrentPage(totalPages)}
                        className="px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                      >
                        {totalPages}
                      </button>
                    </>
                  )}

                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                    }
                    disabled={currentPage === totalPages}
                    className={`px-3 py-2 text-sm rounded ${
                      currentPage === totalPages
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                    }`}
                  >
                    Next
                  </button>
                </div>

                <div className="text-center text-sm text-gray-600 mt-4">
                  Showing{" "}
                  {totalResults > 0
                    ? (currentPage - 1) * resultsPerPage + 1
                    : 0}
                  —{Math.min(currentPage * resultsPerPage, totalResults)} of{" "}
                  {totalResults} results
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const ProductListing: React.FC = () => {
  return (
    <Suspense
      fallback={
        <div className="bg-gray-50 min-h-screen">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg mb-2">Loading...</div>
              <div className="text-gray-400 text-sm">
                Please wait while we load the page
              </div>
            </div>
          </div>
        </div>
      }
    >
      <ProductListingContent />
    </Suspense>
  );
};

export default ProductListing;
