"use client";

import React, { useState, useEffect } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDes<PERSON>,
  SheetFooter,
  Sheet<PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { OrderService } from "@/services/order.service";
import { Order, OrderStatus, ShippingAddress, ShippingDetails } from "@/data/models/order.model";
import { format } from "date-fns";
import { MoreVertical, Edit, Package, MapPin, Ban, Clock, NotebookPen, Undo2, Search, X, Eye } from "lucide-react";

// Status badge color mapping
const getStatusColor = (status: OrderStatus) => {
  switch (status) {
    case "pending":
      return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
    case "confirmed":
      return "bg-blue-100 text-blue-800 hover:bg-blue-100";
    case "processing":
      return "bg-purple-100 text-purple-800 hover:bg-purple-100";
    case "shipped":
      return "bg-orange-100 text-orange-800 hover:bg-orange-100";
    case "delivered":
      return "bg-green-100 text-green-800 hover:bg-green-100";
    case "cancelled":
      return "bg-red-100 text-red-800 hover:bg-red-100";
    default:
      return "bg-gray-100 text-gray-800 hover:bg-gray-100";
  }
};

// Format currency
const formatCurrency = (amount: number, currency: string) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
  }).format(amount);
};

// Skeleton loader component
const TableSkeleton = () => {
  return (
    <div className="space-y-3">
      {Array.from({ length: 10 }).map((_, index) => (
        <div key={index} className="flex space-x-4">
          <Skeleton className="h-4 w-[120px]" />
          <Skeleton className="h-4 w-[80px]" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[120px]" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[120px]" />
          <Skeleton className="h-4 w-[40px]" />
        </div>
      ))}
    </div>
  );
};

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalOrders, setTotalOrders] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [filterStatus, setFilterStatus] = useState<OrderStatus>("pending");
  const [searchTerm, setSearchTerm] = useState("");

  // Sheet and Dialog states
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isStatusSheetOpen, setIsStatusSheetOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isAdminNotesDialogOpen, setIsAdminNotesDialogOpen] = useState(false);
  const [isShippingDetailsDialogOpen, setIsShippingDetailsDialogOpen] = useState(false);
  const [isShippingAddressDialogOpen, setIsShippingAddressDialogOpen] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);

  // Form states
  const [newStatus, setNewStatus] = useState<OrderStatus>("pending");
  const [statusNotes, setStatusNotes] = useState("");
  const [adminNotes, setAdminNotes] = useState("");
  const [shippingDetails, setShippingDetails] = useState<Partial<ShippingDetails>>({});
  const [shippingAddress, setShippingAddress] = useState<Partial<ShippingAddress>>({});
  const [cancelReason, setCancelReason] = useState("");

  // Loading states for actions
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [isUpdatingNotes, setIsUpdatingNotes] = useState(false);
  const [isUpdatingShipping, setIsUpdatingShipping] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);

  const orderService = new OrderService();
  const itemsPerPage = 10;

  // Action handlers
  const handleOpenStatusSheet = async (order: Order) => {
    setSelectedOrder(order);
    setNewStatus(order.status);
    setStatusNotes("");
    
    // Fetch full order details to get status history
    try {
      const fullOrder = await orderService.getOrderById(order.id);
      if (fullOrder) {
        setSelectedOrder(fullOrder);
      }
    } catch (error) {
      console.error("Error fetching order details:", error);
    }
    
    setIsStatusSheetOpen(true);
  };

  const handleOpenViewDialog = async (order: Order) => {
    setSelectedOrder(order);
    try {
      const fullOrder = await orderService.getOrderById(order.id);
      if (fullOrder) {
        setSelectedOrder(fullOrder);
      }
    } catch (error) {
      console.error("Error fetching order details:", error);
    }
    setIsViewDialogOpen(true);
  };

  const handleUpdateStatus = async () => {
    if (!selectedOrder) return;
    
    setIsUpdatingStatus(true);
    try {
      const success = await orderService.updateOrderStatus(
        selectedOrder.id,
        newStatus,
        statusNotes || undefined,
        "admin" // You might want to get this from auth context
      );
      
      if (success) {
        setIsStatusSheetOpen(false);
        await fetchOrders(currentPage);
      } else {
        setError("Failed to update order status");
      }
    } catch (error) {
      console.error("Error updating status:", error);
      setError("Failed to update order status");
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const handleOpenAdminNotes = (order: Order) => {
    setSelectedOrder(order);
    setAdminNotes(order.admin_notes || "");
    setIsAdminNotesDialogOpen(true);
  };

  const handleUpdateAdminNotes = async () => {
    if (!selectedOrder) return;
    
    setIsUpdatingNotes(true);
    try {
      const success = await orderService.addAdminNote(selectedOrder.id, adminNotes);
      
      if (success) {
        setIsAdminNotesDialogOpen(false);
        await fetchOrders(currentPage);
      } else {
        setError("Failed to update admin notes");
      }
    } catch (error) {
      console.error("Error updating admin notes:", error);
      setError("Failed to update admin notes");
    } finally {
      setIsUpdatingNotes(false);
    }
  };

  const handleOpenShippingDetails = (order: Order) => {
    setSelectedOrder(order);
    setShippingDetails(order.shipping_details || {});
    setIsShippingDetailsDialogOpen(true);
  };

  const handleUpdateShippingDetails = async () => {
    if (!selectedOrder) return;
    
    setIsUpdatingShipping(true);
    try {
      const success = await orderService.updateShippingDetails(selectedOrder.id, shippingDetails);
      
      if (success) {
        setIsShippingDetailsDialogOpen(false);
        await fetchOrders(currentPage);
      } else {
        setError("Failed to update shipping details");
      }
    } catch (error) {
      console.error("Error updating shipping details:", error);
      setError("Failed to update shipping details");
    } finally {
      setIsUpdatingShipping(false);
    }
  };

  const handleOpenShippingAddress = (order: Order) => {
    setSelectedOrder(order);
    setShippingAddress(order.shipping_address || {});
    setIsShippingAddressDialogOpen(true);
  };

  const handleUpdateShippingAddress = async () => {
    if (!selectedOrder) return;
    
    setIsUpdatingShipping(true);
    try {
      const success = await orderService.modifyShippingAddress(selectedOrder.id, shippingAddress);
      
      if (success) {
        setIsShippingAddressDialogOpen(false);
        await fetchOrders(currentPage);
      } else {
        setError("Failed to update shipping address");
      }
    } catch (error) {
      console.error("Error updating shipping address:", error);
      setError("Failed to update shipping address");
    } finally {
      setIsUpdatingShipping(false);
    }
  };

  const handleOpenCancelDialog = (order: Order) => {
    setSelectedOrder(order);
    setCancelReason("");
    setIsCancelDialogOpen(true);
  };

  const handleCancelOrder = async () => {
    if (!selectedOrder) return;
    
    setIsCancelling(true);
    try {
      const success = await orderService.cancelOrder(
        selectedOrder.id,
        cancelReason || undefined,
        "admin" // You might want to get this from auth context
      );
      
      if (success) {
        setIsCancelDialogOpen(false);
        await fetchOrders(currentPage);
      } else {
        setError("Failed to cancel order");
      }
    } catch (error) {
      console.error("Error cancelling order:", error);
      setError("Failed to cancel order");
    } finally {
      setIsCancelling(false);
    }
  };

  // Filter handlers
  const handleStatusChange = (status: OrderStatus) => {
    setFilterStatus(status);
    setCurrentPage(1); // Reset to first page when filtering
    fetchOrders(1, status, searchTerm);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
    fetchOrders(1, filterStatus, searchTerm);
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    setCurrentPage(1);
    fetchOrders(1, filterStatus, undefined);
  };

  // Table columns definition
  const columns: ColumnDef<Order>[] = [
    {
      accessorKey: "order_number",
      header: "Order Number",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("order_number")}</div>
      ),
    },
    {
      accessorKey: "user_id",
      header: "Customer ID",
      cell: ({ row }) => (
        <div className="text-sm text-gray-600">
          {row.getValue("user_id") as number}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as OrderStatus;
        return (
          <Badge className={getStatusColor(status)}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        );
      },
    },
    {
      accessorKey: "total_amount",
      header: "Total Amount",
      cell: ({ row }) => {
        const amount = row.getValue("total_amount") as number;
        const currency = row.original.currency;
        return (
          <div className="font-medium">
            {formatCurrency(amount, currency)}
          </div>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: "Order Date",
      cell: ({ row }) => {
        const date = new Date(row.getValue("created_at"));
        return (
          <div className="text-sm">
            {format(date, "MMM dd, yyyy")}
            <div className="text-xs text-gray-500">
              {format(date, "HH:mm")}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "shipping_address",
      header: "Shipping Location",
      cell: ({ row }) => {
        const address = row.original.shipping_address;
        return (
          <div className="text-sm">
            <div>{address.city}</div>
            <div className="text-xs text-gray-500">{address.country}</div>
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleOpenViewDialog(order)}>
                <Eye className="mr-2 h-4 w-4" />
                View Order
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleOpenStatusSheet(order)}>
                <Clock className="mr-2 h-4 w-4" />
                Order Status
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <Edit className="mr-2 h-4 w-4" />
                  Update Order
                </DropdownMenuSubTrigger>
                <DropdownMenuSubContent>
                  <DropdownMenuItem onClick={() => handleOpenAdminNotes(order)}>
                    <NotebookPen className="mr-2 h-4 w-4" />
                    Admin Notes
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleOpenShippingDetails(order)}>
                    <Package className="mr-2 h-4 w-4" />
                    Shipping Details
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleOpenShippingAddress(order)}>
                    <MapPin className="mr-2 h-4 w-4" />
                    Shipping Address
                  </DropdownMenuItem>
                </DropdownMenuSubContent>
              </DropdownMenuSub>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                disabled
                className="text-red-600"
              >
                <Undo2 className="mr-2 h-4 w-4" />
                Process Refund
              </DropdownMenuItem>
               <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleOpenCancelDialog(order)}
                className="text-red-600"
              >
                <Ban className="mr-2 h-4 w-4" />
                Cancel Order
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  // Fetch orders
  const fetchOrders = async (page: number = 1, status?: OrderStatus, search?: string) => {
    try {
      setLoading(true);
      setError(null);
      const result = await orderService.listOrders(
        status || filterStatus,
        search || searchTerm || undefined,
        page,
        itemsPerPage
      );
      setOrders(result.orders);
      setTotalOrders(result.total);
    } catch (err) {
      console.error("Error fetching orders:", err);
      setError("Failed to fetch orders. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Initialize table
  const table = useReactTable({
    data: orders,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  // Fetch orders on component mount and page change
  useEffect(() => {
    fetchOrders(currentPage, filterStatus, searchTerm);
  }, [currentPage]);

  // Initial load with default filters
  useEffect(() => {
    fetchOrders(1, filterStatus, searchTerm);
  }, []);

  // Calculate pagination info
  const totalPages = Math.ceil(totalOrders / itemsPerPage);
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalOrders);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Orders</h1>
          <p className="text-muted-foreground">
            Manage and view all customer orders
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle>All Orders</CardTitle>
              {!loading && (
                <p className="text-sm text-muted-foreground">
                  Showing {startItem} to {endItem} of {totalOrders} orders
                </p>
              )}
            </div>
            
            {/* Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
              <div className="flex flex-col space-y-1">
                <Label htmlFor="status-filter" className="text-xs font-medium text-muted-foreground">
                  Status
                </Label>
                <Select value={filterStatus} onValueChange={handleStatusChange}>
                  <SelectTrigger className="w-[140px]" id="status-filter">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex flex-col space-y-1">
                <Label htmlFor="search-input" className="text-xs font-medium text-muted-foreground">
                  Search
                </Label>
                <div className="flex gap-1">
                  <Input
                    id="search-input"
                    placeholder="Order # or User ID..."
                    value={searchTerm}
                    onChange={handleSearchChange}
                    className="w-[200px]"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleSearch();
                      }
                    }}
                  />
                  <Button onClick={handleSearch} variant="outline">
                    <Search className="h-4 w-4" />
                  </Button>
                  {searchTerm && (
                    <Button onClick={handleClearSearch} variant="outline">
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-8">
              <p className="text-red-500">{error}</p>
              <button
                onClick={() => fetchOrders(currentPage, filterStatus, searchTerm)}
                className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Retry
              </button>
            </div>
          ) : loading ? (
            <TableSkeleton />
          ) : orders.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No orders found.</p>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <TableHead key={header.id}>
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                          </TableHead>
                        ))}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {table.getRowModel().rows?.length ? (
                      table.getRowModel().rows.map((row) => (
                        <TableRow
                          key={row.id}
                          data-state={row.getIsSelected() && "selected"}
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id}>
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className="h-24 text-center"
                        >
                          No results.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between pt-4">
                  <div className="text-sm text-muted-foreground">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* View Order Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Order {selectedOrder?.order_number}</DialogTitle>
            <DialogDescription>
              Overview of order details
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-2">
            {/* Status and Confirmed At */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <div className="text-xs text-muted-foreground">Status</div>
                <Badge className={getStatusColor(selectedOrder?.status || 'pending')}>
                  {selectedOrder?.status ? selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1) : ''}
                </Badge>
              </div>
              <div>
                <div className="text-xs text-muted-foreground">Confirmed At</div>
                <div className="text-sm">
                  {selectedOrder?.confirmed_at ? format(new Date(selectedOrder.confirmed_at), 'MMM dd, yyyy HH:mm') : '—'}
                </div>
              </div>
              <div>
                <div className="text-xs text-muted-foreground">Total Paid</div>
                <div className="text-sm font-medium">
                  {selectedOrder ? formatCurrency(selectedOrder.total_amount, selectedOrder.currency) : ''}
                </div>
              </div>
            </div>

            {/* Items */}
            <div>
              <h4 className="text-sm font-medium mb-2">Items</h4>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item</TableHead>
                      <TableHead>Qty</TableHead>
                      <TableHead>Unit Price</TableHead>
                      <TableHead>Line Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedOrder?.items?.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div className="text-sm font-medium">{item.product_snapshot.title}</div>
                          {item.variant_snapshot && (
                            <div className="text-xs text-muted-foreground">
                              {Object.entries(item.variant_snapshot.option_values).map(([k, v]) => `${k}: ${v}`).join(', ')}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{formatCurrency(item.unit_price, item.currency)}</TableCell>
                        <TableCell>{formatCurrency(item.line_total, item.currency)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Order Status Sheet */}
      <Sheet open={isStatusSheetOpen} onOpenChange={setIsStatusSheetOpen}>
        <SheetContent className="w-[400px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle>Update Order Status</SheetTitle>
            <SheetDescription>
              Update the status of order {selectedOrder?.order_number}
            </SheetDescription>
          </SheetHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select value={newStatus} onValueChange={(value: OrderStatus) => setNewStatus(value)}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="notes"
                value={statusNotes}
                onChange={(e) => setStatusNotes(e.target.value)}
                placeholder="Optional notes about this status change"
                className="col-span-3"
              />
            </div>
          </div>

          {/* Status History */}
          {selectedOrder?.status_history && (
            <div className="mt-6">
              <h4 className="text-sm font-medium mb-3">Status History</h4>
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Status</TableHead>
                      <TableHead>Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedOrder.status_history.map((entry, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Badge className={getStatusColor(entry.status)}>
                            {entry.status.charAt(0).toUpperCase() + entry.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm">
                          {format(new Date(entry.timestamp), "MMM dd, yyyy HH:mm")}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}

          <SheetFooter>
            <Button 
              type="submit" 
              onClick={handleUpdateStatus}
              disabled={isUpdatingStatus}
            >
              {isUpdatingStatus ? "Updating..." : "Update Status"}
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* Admin Notes Dialog */}
      <Dialog open={isAdminNotesDialogOpen} onOpenChange={setIsAdminNotesDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Update Admin Notes</DialogTitle>
            <DialogDescription>
              Add or update admin notes for order {selectedOrder?.order_number}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="admin-notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="admin-notes"
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                className="col-span-3"
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              type="submit" 
              onClick={handleUpdateAdminNotes}
              disabled={isUpdatingNotes}
            >
              {isUpdatingNotes ? "Updating..." : "Update Notes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Shipping Details Dialog */}
      <Dialog open={isShippingDetailsDialogOpen} onOpenChange={setIsShippingDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Update Shipping Details</DialogTitle>
            <DialogDescription>
              Update shipping details for order {selectedOrder?.order_number}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="tracking" className="text-right">
                Tracking #
              </Label>
              <Input
                id="tracking"
                value={shippingDetails.tracking_number || ""}
                onChange={(e) => setShippingDetails({...shippingDetails, tracking_number: e.target.value})}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="provider" className="text-right">
                Provider
              </Label>
              <Input
                id="provider"
                value={shippingDetails.provider || ""}
                onChange={(e) => setShippingDetails({...shippingDetails, provider: e.target.value})}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="shipping-notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="shipping-notes"
                value={shippingDetails.notes || ""}
                onChange={(e) => setShippingDetails({...shippingDetails, notes: e.target.value})}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              type="submit" 
              onClick={handleUpdateShippingDetails}
              disabled={isUpdatingShipping}
            >
              {isUpdatingShipping ? "Updating..." : "Update Details"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Shipping Address Dialog */}
      <Dialog open={isShippingAddressDialogOpen} onOpenChange={setIsShippingAddressDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Update Shipping Address</DialogTitle>
            <DialogDescription>
              Update shipping address for order {selectedOrder?.order_number}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="recipient" className="text-right">
                Recipient
              </Label>
              <Input
                id="recipient"
                value={shippingAddress.recipient_name || ""}
                onChange={(e) => setShippingAddress({...shippingAddress, recipient_name: e.target.value})}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="address1" className="text-right">
                Address 1
              </Label>
              <Input
                id="address1"
                value={shippingAddress.address_line_1 || ""}
                onChange={(e) => setShippingAddress({...shippingAddress, address_line_1: e.target.value})}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="city" className="text-right">
                City
              </Label>
              <Input
                id="city"
                value={shippingAddress.city || ""}
                onChange={(e) => setShippingAddress({...shippingAddress, city: e.target.value})}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="country" className="text-right">
                Country
              </Label>
              <Input
                id="country"
                value={shippingAddress.country || ""}
                onChange={(e) => setShippingAddress({...shippingAddress, country: e.target.value})}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              type="submit" 
              onClick={handleUpdateShippingAddress}
              disabled={isUpdatingShipping}
            >
              {isUpdatingShipping ? "Updating..." : "Update Address"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cancel Order Dialog */}
      <Dialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Cancel Order</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel order {selectedOrder?.order_number}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="cancel-reason" className="text-right">
                Reason
              </Label>
              <Textarea
                id="cancel-reason"
                value={cancelReason}
                onChange={(e) => setCancelReason(e.target.value)}
                placeholder="Optional reason for cancellation"
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCancelDialogOpen(false)}>
              Keep Order
            </Button>
            <Button 
              variant="destructive"
              onClick={handleCancelOrder}
              disabled={isCancelling}
            >
              {isCancelling ? "Cancelling..." : "Cancel Order"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}