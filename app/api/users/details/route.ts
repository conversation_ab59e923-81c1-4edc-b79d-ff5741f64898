import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * GET /api/users/details
 * Query params:
 *   - userId: string (required)
 * Returns user details (from mp_users) and address (from users_address)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const userId = decodeURIComponent(searchParams.get("userId") || "");

    if (!userId) {
      return NextResponse.json(
        { error: "userId is required." },
        { status: 400 },
      );
    }

    // Fetch user data from mp_users
    const { data: userData, error: userError } = await supabase
      .from("mp_users")
      .select("*")
      .eq("id", userId)
      .single();

    if (userError) {
      return NextResponse.json(
        { error: "Error fetching user data.", details: userError.message },
        { status: 500 },
      );
    }

    if (!userData) {
      return NextResponse.json({ error: "User not found." }, { status: 404 });
    }

    // Fetch address data from users_address
    const { data: addressData, error: addressError } = await supabase
      .from("users_address")
      .select("*")
      .eq("user_id", userId)
      .single();

    // Combine user data and address data
    const userDetails = {
      id: userData.id,
      first_name: userData.first_name || "",
      last_name: userData.last_name || "",
      phone_number: userData.phone_number || "",
      email: userData.email || "",
      receive_marketing: userData.receive_marketing || false,
      signed_shipping_policy: userData.signed_shipping_policy || false,
      signed_privacy_policy: userData.signed_privacy_policy || false,
      country_code: userData.country_code || "",
      deliverTo: addressData?.DeliverTo || "",
      company: addressData?.Company || "",
      address: addressData?.Address || "",
      city: addressData?.City || "",
      stateProvince: addressData?.StateProvince || "",
      postalCode: addressData?.PostalCode || "",
    };

    return NextResponse.json({ data: userDetails });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
