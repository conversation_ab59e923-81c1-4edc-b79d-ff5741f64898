import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { UserAddressModel } from "@/services/address.service";

export async function GET(
  request: NextRequest,
  { params }: { params: { uuid: string } },
) {
  const supabase = await createClient();

  // Get authenticated user info, but tolerate unauthenticated callers.
  const { data: authData } = await supabase.auth.getUser();

  // If there is no authenticated user, return a guest-safe empty JSON object.
  // This prevents front-end `.json()` parse errors and allows shop pages to
  // function with sensible fallbacks (e.g., default currency).
  if (!authData || !authData.user) {
    return NextResponse.json({}, { status: 200 });
  }

  const { data, error } = await supabase
    .from("users_address")
    .select("*")
    .eq("uuid", authData.user.id)
    .single();
  if (error) {
    return new NextResponse("Error fetching address", { status: 500 });
  }
  return NextResponse.json(data as UserAddressModel);
}

export async function UPDATE(
  request: NextRequest,
  { params }: { params: { uuid: string } },
) {
  const supabase = await createClient();

  const user = await supabase.auth.getUser();
  if (!user || !user.data || !user.data.user) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const body = await request.json();

  const uuid = user.data.user.id;

  const { data, error } = await supabase
    .from("users_address")
    .upsert({ ...body, uuid }, { onConflict: "uuid" }) // assumes uuid is unique or primary key
    .select()
    .single();

  if (error) {
    return new NextResponse("Error updating address", { status: 500 });
  }

  return NextResponse.json(data as UserAddressModel);
}

export async function POST(
  request: NextRequest,
  { params }: { params: { uuid: string } },
) {
  const supabase = await createClient();

  const user = await supabase.auth.getUser();
  if (!user || !user.data || !user.data.user) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const body = await request.json();
  const uuid = user.data.user.id;

  // Optionally, check if an address already exists for this user
  const { data: existingAddress, error: fetchError } = await supabase
    .from("users_address")
    .select("uuid")
    .eq("uuid", uuid)
    .single();

  if (existingAddress) {
    return new NextResponse("Address already exists for this user", {
      status: 409,
    });
  }

  const addressWithUuid = { ...body, uuid };

  const { data, error } = await supabase
    .from("users_address")
    .insert(addressWithUuid)
    .select()
    .single();

  if (error) {
    return new NextResponse("Error creating address", { status: 500 });
  }

  return NextResponse.json(data as UserAddressModel, { status: 201 });
}
