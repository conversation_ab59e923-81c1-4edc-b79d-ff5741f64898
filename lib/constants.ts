const countryList = [
  { label: "Botswana", value: "Botswana" },
  { label: "Egypt", value: "Egypt" },
  { label: "Ghana", value: "Ghana" },
  { label: "India", value: "India" },
  { label: "Indonesia", value: "Indonesia" },
  { label: "Kenya", value: "Kenya" },
  { label: "Kuwait", value: "Kuwait" },
  { label: "Liberia", value: "Liberia" },
  { label: "Malawi", value: "Malawi" },
  { label: "Malaysia", value: "Malaysia" },
  { label: "Namibia", value: "Namibia" },
  { label: "Nigeria", value: "Nigeria" },
  { label: "Pakistan", value: "Pakistan" },
  { label: "Philippines", value: "Philippines" },
  { label: "Rwanda", value: "Rwanda" },
  { label: "Saudi Arabia", value: "Saudi Arabia" },
  { label: "Sierra Leone", value: "Sierra Leone" },
  { label: "Singapore", value: "Singapore" },
  { label: "South Africa", value: "South Africa" },
  { label: "South Sudan", value: "South Sudan" },
  { label: "Tanzania", value: "Tanzania" },
  { label: "Thailand", value: "Thailand" },
  { label: "UAE", value: "UAE" },
  { label: "United Kingdom", value: "United Kingdom" },
  { label: "Zambia", value: "Zambia" },
  { label: "Zimbabwe", value: "Zimbabwe" },
] as const;
//For shipping rates.
const shipping_rate_countries = {
  DoorToDoor: ["Ghana", "Nigeria"],
  RestOfAfrica: ["Botswana", "Egypt", "Kenya", "Liberia", "Malawi", "Namibia", "Rwanda", "Sierra Leone", "South Africa", "South Sudan", "Tanzania", "Zambia", "Zimbabwe"],
} as const;

const country_currencies = {
  BWP: "Botswana",
  EGP: "Egypt",
  GHS: "Ghana",
  INR: "India",
  IDR: "Indonesia",
  KES: "Kenya",
  KWD: "Kuwait",
  LRD: "Liberia",
  MWK: "Malawi",
  MYR: "Malaysia",
  NAD: "Namibia",
  NGN: "Nigeria",
  PKR: "Pakistan",
  PHP: "Philippines",
  RWF: "Rwanda",
  SAR: "Saudi Arabia",
  SLL: "Sierra Leone",
  SGD: "Singapore",
  ZAR: "South Africa",
  SSP: "South Sudan",
  TZS: "Tanzania",
  THB: "Thailand",
  AED: "UAE",
  GBP: "United Kingdom",
  ZMW: "Zambia",
  ZWL: "Zimbabwe"
} as const;

const categories = [
  {
    "id": "6242369a-7f1f-49ba-9bc7-aae34088ec25",
    "name": "Electronics",
    "parent_id": null
  },
  {
    "id": "bdc66b83-ba65-4a79-9c07-41f3cb19f917",
    "name": "Fashion & Apparel",
    "parent_id": null
  },
  {
    "id": "9f28e346-7178-474b-82fd-93fce04f6c7f",
    "name": "Tools & DIY",
    "parent_id": null
  },
  {
    "id": "45fa1451-ebc1-4a59-93c0-828c789a9ca4",
    "name": "Baby & Kids",
    "parent_id": null
  },
  {
    "id": "9c13942d-6e48-4883-8630-f48b4fe812a5",
    "name": "Party Supplies",
    "parent_id": null
  },
  {
    "id": "a57ab1fa-9b5e-4062-a1ee-0a77ca3fd421",
    "name": "Groceries & Gourmet Food",
    "parent_id": null
  },
  {
    "id": "5c81267b-f078-4bec-bc26-44b7a7b6d1fd",
    "name": "Arts, Crafts & Sewing",
    "parent_id": null
  },
  {
    "id": "2b9e2957-7f7a-4b83-baa1-0f7b77be0875",
    "name": "Office Supplies",
    "parent_id": null
  },
  {
    "id": "63aec868-692c-40ce-8add-2b34726a3f50",
    "name": "Health & Beauty",
    "parent_id": null
  },
  {
    "id": "a321c729-e1e1-4d6a-86a2-e552076c91e1",
    "name": "iPhone",
    "parent_id": "6242369a-7f1f-49ba-9bc7-aae34088ec25"
  },
  {
    "id": "6fda7fcf-f8a1-43e8-a878-26e967099472",
    "name": "Baby Gear",
    "parent_id": "45fa1451-ebc1-4a59-93c0-828c789a9ca4"
  },
  {
    "id": "1b4bb291-9ae8-4605-98ae-ccaf2a9debd1",
    "name": "Computers & Accessories",
    "parent_id": "6242369a-7f1f-49ba-9bc7-aae34088ec25"
  },
  {
    "id": "a754776b-0c1e-4ed6-929c-9875cb84a189",
    "name": "Painting",
    "parent_id": "5c81267b-f078-4bec-bc26-44b7a7b6d1fd"
  },
  {
    "id": "813d8100-e914-4ca8-b5eb-73ab6de39c44",
    "name": "Party Decorations",
    "parent_id": "9c13942d-6e48-4883-8630-f48b4fe812a5"
  },
  {
    "id": "807b1555-1f77-43c3-93f3-9570eaeb345a",
    "name": "Power Tools",
    "parent_id": "9f28e346-7178-474b-82fd-93fce04f6c7f"
  },
  {
    "id": "566cbfd6-6dee-4b28-9e0f-511713517f6b",
    "name": "Snacks",
    "parent_id": "a57ab1fa-9b5e-4062-a1ee-0a77ca3fd421"
  },
  {
    "id": "32f30fa5-893c-4724-922c-fc0c3136e03b",
    "name": "Women's Clothing",
    "parent_id": "bdc66b83-ba65-4a79-9c07-41f3cb19f917"
  },
  {
    "id": "cfe99d81-5fce-424f-af9e-ed5a2bb2206d",
    "name": "Writing Supplies",
    "parent_id": "2b9e2957-7f7a-4b83-baa1-0f7b77be0875"
  },
  {
    "id": "0a1cc4c7-30e2-4c22-aebc-1ede59de8cab",
    "name": "Beverages",
    "parent_id": "a57ab1fa-9b5e-4062-a1ee-0a77ca3fd421"
  },
  {
    "id": "f7f76f10-a738-4415-b718-5c62ff34ba20",
    "name": "Drawing",
    "parent_id": "5c81267b-f078-4bec-bc26-44b7a7b6d1fd"
  },
  {
    "id": "c48cbd27-d34c-48d4-894f-594ad064a874",
    "name": "Feeding",
    "parent_id": "45fa1451-ebc1-4a59-93c0-828c789a9ca4"
  },
  {
    "id": "c92675e0-4a16-45c3-9db4-166f2ba531e3",
    "name": "Hand Tools",
    "parent_id": "9f28e346-7178-474b-82fd-93fce04f6c7f"
  },
  {
    "id": "1ed5cf95-66f8-4b89-b4e6-9911efd6929d",
    "name": "Mobile Phones & Accessories",
    "parent_id": "6242369a-7f1f-49ba-9bc7-aae34088ec25"
  },
  {
    "id": "d1108551-538a-45ca-acde-e6dc1cae9c78",
    "name": "Office Paper",
    "parent_id": "2b9e2957-7f7a-4b83-baa1-0f7b77be0875"
  },
  {
    "id": "2daf4d22-1763-415d-8241-b28a79630e12",
    "name": "Party Tableware",
    "parent_id": "9c13942d-6e48-4883-8630-f48b4fe812a5"
  },
  {
    "id": "9db72a5e-e167-4fe3-ae96-479f2e96c51e",
    "name": "Balloons",
    "parent_id": "9c13942d-6e48-4883-8630-f48b4fe812a5"
  },
  {
    "id": "247d812c-beca-4517-88b7-550cfd48c938",
    "name": "Binders & Folders",
    "parent_id": "2b9e2957-7f7a-4b83-baa1-0f7b77be0875"
  },
  {
    "id": "a252cf40-f78f-40b7-833a-8ba98564f32c",
    "name": "Pantry Staples",
    "parent_id": "a57ab1fa-9b5e-4062-a1ee-0a77ca3fd421"
  },
  {
    "id": "535dbbf6-c877-417e-a026-a172105b3d00",
    "name": "Sewing",
    "parent_id": "5c81267b-f078-4bec-bc26-44b7a7b6d1fd"
  },
  {
    "id": "8bfaec32-b566-4616-98a3-977569bfe760",
    "name": "Bottle Feeding",
    "parent_id": "c48cbd27-d34c-48d4-894f-594ad064a874"
  },
  {
    "id": "0faa6393-9d66-49c0-b002-dce23f836259",
    "name": "Dresses",
    "parent_id": "32f30fa5-893c-4724-922c-fc0c3136e03b"
  },
  {
    "id": "b16407fc-c211-415d-93d3-fbbaedc70283",
    "name": "Drills",
    "parent_id": "807b1555-1f77-43c3-93f3-9570eaeb345a"
  },
  {
    "id": "28b602fc-85f9-4077-82e9-fc4f38b47a17",
    "name": "Hammers",
    "parent_id": "c92675e0-4a16-45c3-9db4-166f2ba531e3"
  },
  {
    "id": "d288502f-d683-4983-828a-bfaa87ab8418",
    "name": "Laptops",
    "parent_id": "6242369a-7f1f-49ba-9bc7-aae34088ec25"
  },
  {
    "id": "063153db-4f1c-41fe-9753-69169e043127",
    "name": "Smartphones",
    "parent_id": "1ed5cf95-66f8-4b89-b4e6-9911efd6929d"
  },
  {
    "id": "9fca5531-ffde-4dd7-a37b-69f7bee3a215",
    "name": "Strollers",
    "parent_id": "6fda7fcf-f8a1-43e8-a878-26e967099472"
  },
  {
    "id": "a295ba27-0ddf-43b0-b9e0-1b74767dcae5",
    "name": "Car Seats",
    "parent_id": "6fda7fcf-f8a1-43e8-a878-26e967099472"
  },
  {
    "id": "be641008-f878-49a3-88e3-d8b404d7ca09",
    "name": "Desktop Computers",
    "parent_id": "1b4bb291-9ae8-4605-98ae-ccaf2a9debd1"
  },
  {
    "id": "5a504eb0-bd18-447e-ae9d-0d99c0a4f333",
    "name": "Highchairs",
    "parent_id": "c48cbd27-d34c-48d4-894f-594ad064a874"
  },
  {
    "id": "41a703f4-c281-4ca2-adc3-317020ecc751",
    "name": "Phone Cases & Covers",
    "parent_id": "1ed5cf95-66f8-4b89-b4e6-9911efd6929d"
  },
  {
    "id": "07cd0286-93d2-4b46-b8b1-997219975374",
    "name": "Saws",
    "parent_id": "807b1555-1f77-43c3-93f3-9570eaeb345a"
  },
  {
    "id": "8e8ba87c-7fea-4e5f-9f37-d5864910a58b",
    "name": "Screwdrivers",
    "parent_id": "c92675e0-4a16-45c3-9db4-166f2ba531e3"
  },
  {
    "id": "fcd2f88f-3bbc-4cd7-b260-e81a7f3554f3",
    "name": "Tops & Blouses",
    "parent_id": "32f30fa5-893c-4724-922c-fc0c3136e03b"
  },
  {
    "id": "90705d7a-e614-4926-97eb-73f88f5853b6",
    "name": "Baby Carriers",
    "parent_id": "6fda7fcf-f8a1-43e8-a878-26e967099472"
  },
  {
    "id": "28eca42e-78f2-4e15-840e-80c98cfc7f20",
    "name": "Grinders",
    "parent_id": "807b1555-1f77-43c3-93f3-9570eaeb345a"
  },
  {
    "id": "356d1e6e-d5b2-405b-9145-d6b22080407f",
    "name": "Wrenches",
    "parent_id": "c92675e0-4a16-45c3-9db4-166f2ba531e3"
  },
  {
    "id": "afc5f8d9-824f-4440-b102-ee58f9bee076",
    "name": "Speakers",
    "parent_id": "6242369a-7f1f-49ba-9bc7-aae34088ec25"
  },
  {
    "id": "0d60c824-626f-4416-add6-204c1a170679",
    "name": "Energy Bars",
    "parent_id": "63aec868-692c-40ce-8add-2b34726a3f50"
  },
  {
    "id": "200954c5-cf83-4ab7-86e2-f29329cd72de",
    "name": "Personal Care",
    "parent_id": "63aec868-692c-40ce-8add-2b34726a3f50"
  },
  {
    "id": "4b32aea9-3c26-4118-adb6-b81252b62f9e",
    "name": "Mens Clothing",
    "parent_id": "bdc66b83-ba65-4a79-9c07-41f3cb19f917"
  },
  {
    "id": "a5999e7c-495a-469a-b3b8-c8456e39b2ad",
    "name": "Clothing Accessories",
    "parent_id": "bdc66b83-ba65-4a79-9c07-41f3cb19f917"
  },
  {
    "id": "d223dea2-9621-4e67-8997-472aedb80350",
    "name": "Charger",
    "parent_id": "6242369a-7f1f-49ba-9bc7-aae34088ec25"
  },


]

export {countryList, country_currencies, categories, shipping_rate_countries};