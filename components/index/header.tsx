"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  ChevronDown,
  ChevronUp,
  Home,
  LogOut,
  User,
  ShoppingCart,
  Search,
  Container,
  Shield,
} from "lucide-react";
import { MPDock } from "./mp-dock";
import MPSparklesText from "./mp-sparkles-text";
import { useCart } from "@/lib/CartContext";
import { AuthService } from "@/services/auth.service";
import { supabase } from "@/lib/supabase_client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function Header(): JSX.Element {
  // Prevent hydration mismatches by rendering a lightweight placeholder on first render.
  // We mark mounted only on the client in useEffect and render the full header afterwards.
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  const pathname = usePathname();
  const router = useRouter();
  const cart = useCart();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const authService = new AuthService();

  const [userData, setUserData] = useState<{
    first_name?: string;
    last_name?: string;
    email?: string;
  } | null>(null);

  const toggleMenu = () => {
    setIsMenuOpen((s) => !s);
    const mobileMenu = document.querySelector("#mobile-menu");
    if (mobileMenu) {
      mobileMenu.classList.toggle("hidden");
    }
  };

  const toggleProfileDropdown = () => {
    setIsProfileDropdownOpen((s) => !s);
  };

  const handleSignout = async () => {
    await authService.signOut();
  };

  const isActivePath = (path: string) =>
    pathname === path
      ? "text-primary font-semibold"
      : "text-gray-700 hover:text-primary";

  useEffect(() => {
    const authUser = localStorage.getItem("__mp_user_data");
    if (authUser) {
      try {
        const parsedUser = JSON.parse(authUser);
        setUserData(parsedUser);
        setIsLoggedIn(true);
      } catch {
        setUserData(null);
        setIsLoggedIn(false);
      }
    } else {
      setUserData(null);
      setIsLoggedIn(false);
    }

    const checkAdminStatus = () => {
      const adminCookie = document.cookie
        .split("; ")
        .find((row) => row.startsWith("_auth_admin="));
      setIsAdmin(Boolean(adminCookie));
    };
    checkAdminStatus();

    // Subscribe to auth changes (defensive unsubscribe handling)
    let unsubscribeFn: (() => void) | null = null;
    try {
      // supabase.auth.onAuthStateChange returns { data: { subscription } } in older SDKs,
      // and an unsubscribe function in newer ones. Handle both shapes defensively.
      const res: any = supabase.auth.onAuthStateChange(() => {
        setTimeout(() => {
          checkAdminStatus();
          const authUserStr = localStorage.getItem("__mp_user_data");
          if (authUserStr) {
            try {
              const parsed = JSON.parse(authUserStr);
              setUserData(parsed);
              setIsLoggedIn(true);
            } catch {
              setUserData(null);
              setIsLoggedIn(false);
            }
          } else {
            setUserData(null);
            setIsLoggedIn(false);
          }
        }, 100);
      });

      // If returned object has data.subscription with unsubscribe method
      if (
        res &&
        res.data &&
        res.data.subscription &&
        typeof res.data.subscription.unsubscribe === "function"
      ) {
        unsubscribeFn = () => res.data.subscription.unsubscribe();
      } else if (typeof res === "function") {
        // If res is an unsubscribe function directly
        unsubscribeFn = res;
      } else if (res && res.unsubscribe) {
        unsubscribeFn = () => res.unsubscribe();
      }
    } catch {
      // ignore subscription errors
      unsubscribeFn = null;
    }

    return () => {
      if (unsubscribeFn) {
        try {
          unsubscribeFn();
        } catch {
          // ignore
        }
      }
    };
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (
        isProfileDropdownOpen &&
        !target.closest(".profile-dropdown-container") &&
        !target.closest('[role="alertdialog"]') &&
        !target.closest('[role="dialog"]')
      ) {
        setIsProfileDropdownOpen(false);
      }
    };

    if (isProfileDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isProfileDropdownOpen]);

  const getDisplayInitials = () => {
    if (userData?.first_name || userData?.last_name) {
      return `${userData?.first_name?.[0] ?? ""}${userData?.last_name?.[0] ?? ""}`;
    }

    // Guard against SSR where `document` is not available
    if (typeof document === "undefined") {
      return "";
    }

    const adminCookie = document.cookie
      .split("; ")
      .find((row) => row.startsWith("_auth_admin="));
    if (adminCookie) {
      try {
        const encoded = adminCookie.split("=")[1] ?? "";
        // decodeURIComponent in case cookie was encoded
        const admin = JSON.parse(decodeURIComponent(encoded));
        return `${admin?.first_name?.[0] ?? ""}${admin?.last_name?.[0] ?? ""}`;
      } catch {
        return "";
      }
    }
    return "";
  };

  const displayInitials = getDisplayInitials();

  const handleSearch = () => {
    if (searchValue.trim()) {
      router.push(
        `/home/<USER>/results?query=${encodeURIComponent(searchValue.trim())}`,
      );
    }
  };

  const cartQuantity =
    cart.items?.reduce((total, item) => total + item.quantity, 0) ?? 0;

  // If the component hasn't mounted on the client yet, return a stable, minimal placeholder
  // so the server HTML and initial client HTML have the same shape and avoid hydration errors.
  if (!mounted) {
    return (
      <header className="sticky top-0 z-50 bg-white shadow-md" aria-hidden>
        <div className="h-[60px]" />
      </header>
    );
  }

  return (
    <header className="sticky top-0 z-50 bg-white shadow-md">
      {/* Desktop Layout */}
      <div className="hidden sm:flex items-center px-4 sm:px-[8%] py-3 min-h-[60px]">
        {/* Left: Logo */}
        <div className="flex items-center">
          <a href="/" onClick={() => setIsMenuOpen(false)}>
            <img
              src="/assets/imgs/logo.svg"
              alt="mailpallet-logo"
              width={120}
              height={40}
              className="h-auto"
            />
          </a>
        </div>

        {/* Center: Search Bar (when logged in) or Navigation (when not logged in) */}
        <div className="flex-1 flex justify-center mx-8">
          {isLoggedIn ? (
            <div className="flex items-center w-full max-w-2xl">
              <Input
                type="text"
                placeholder="Search for products"
                className="flex-1 mr-2"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              />
              <Button
                className="bg-primary hover:bg-blue-800 text-white"
                onClick={handleSearch}
              >
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>
          ) : (
            <div className="flex items-center space-x-8">
              <a
                href="/home/<USER>"
                className={isActivePath("/home/<USER>")}
                onClick={() => setIsMenuOpen(false)}
              >
                Shop
              </a>
              <a
                href="/about"
                className={isActivePath("/about")}
                onClick={() => setIsMenuOpen(false)}
              >
                About
              </a>
              <a
                href="/services"
                className={isActivePath("/services")}
                onClick={() => setIsMenuOpen(false)}
              >
                Services
              </a>
              <a
                href="/shipping"
                className={isActivePath("/shipping")}
                onClick={() => setIsMenuOpen(false)}
              >
                Shipping Calculator
              </a>
              <a
                href="/get-started"
                className={isActivePath("/get-started")}
                onClick={() => setIsMenuOpen(false)}
              >
                <MPSparklesText
                  text="How To Use MailPallet"
                  className="font-normal text-md"
                />
              </a>
            </div>
          )}
        </div>

        {/* Right: Profile/Login and cart (show cart if user logged in OR if cart has items) */}
        <div className="flex items-center space-x-4">
          {isLoggedIn ? (
            <>
              <div className="relative profile-dropdown-container">
                <div
                  onClick={toggleProfileDropdown}
                  className="cursor-pointer"
                  role="button"
                  tabIndex={0}
                  aria-expanded={isProfileDropdownOpen}
                  onKeyDown={(e) =>
                    e.key === "Enter" && toggleProfileDropdown()
                  }
                >
                  <Avatar className="w-10 h-10 hover:ring-2 hover:ring-blue-300 transition-all">
                    <AvatarFallback className="bg-blue-600 text-white font-bold text-sm">
                      {displayInitials}
                    </AvatarFallback>
                  </Avatar>
                </div>

                {isProfileDropdownOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-100 z-50">
                    <div className="space-y-1 p-2">
                      <Link
                        href="/home/<USER>/my-orders"
                        className="flex items-center space-x-3 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                        onClick={() => setIsProfileDropdownOpen(false)}
                      >
                        <Container className="w-4 h-4 text-gray-600" />
                        <span className="text-sm">My Orders</span>
                      </Link>

                      <Link
                        href="/home/<USER>"
                        className="flex items-center space-x-3 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                        onClick={() => setIsProfileDropdownOpen(false)}
                      >
                        <Home className="w-4 h-4 text-gray-600" />
                        <span className="text-sm">Virtual Address</span>
                      </Link>

                      <Link
                        href="/home/<USER>"
                        className="flex items-center space-x-3 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                        onClick={() => setIsProfileDropdownOpen(false)}
                      >
                        <User className="w-4 h-4 text-gray-600" />
                        <span className="text-sm">Profile</span>
                      </Link>

                      {isAdmin && (
                        <Link
                          href="/admin/dashboard"
                          className="flex items-center space-x-3 p-2 rounded-lg hover:bg-green-50 transition-colors"
                          onClick={() => setIsProfileDropdownOpen(false)}
                        >
                          <Shield className="w-4 h-4 text-green-600" />
                          <span className="text-sm text-green-600 font-medium">
                            Admin Dashboard
                          </span>
                        </Link>
                      )}

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <button className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-red-50 text-red-600 transition-colors text-sm">
                            <LogOut className="w-4 h-4 text-red-500" />
                            <span>Log out</span>
                          </button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="max-w-[425px] w-[90vw] rounded-xl">
                          <AlertDialogHeader>
                            <AlertDialogTitle>
                              Are you sure you want to log out?
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              You'll need to sign in again to access your
                              account.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter className="space-x-4">
                            <AlertDialogCancel className="w-full sm:w-auto">
                              Cancel
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={handleSignout}
                              className="w-full sm:w-auto bg-red-600 hover:bg-red-700"
                            >
                              Log out
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                )}
              </div>

              {/* Cart icon for logged-in users */}
              <Link
                href="/home/<USER>/cart"
                className="flex items-center justify-center p-2 hover:bg-blue-100 hover:rounded-md transition-colors relative"
                onClick={() => setIsMenuOpen(false)}
              >
                <ShoppingCart className="w-6 h-6 text-gray-700" />
                {cartQuantity > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                    {cartQuantity}
                  </span>
                )}
              </Link>
            </>
          ) : (
            <>
              {/* When logged out: show cart icon if cart has items */}
              {cartQuantity > 0 && (
                <Link
                  href="/home/<USER>/cart"
                  className="flex items-center justify-center p-2 hover:bg-blue-100 hover:rounded-md transition-colors relative"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <ShoppingCart className="w-6 h-6 text-gray-700" />
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                    {cartQuantity}
                  </span>
                </Link>
              )}

              <Link
                href="/login"
                className="bg-primary hover:bg-blue-800 text-white font-bold py-2 px-4 rounded"
                onClick={() => setIsMenuOpen(false)}
              >
                Register/Login
              </Link>
            </>
          )}
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="sm:hidden bg-white">
        {/* Mobile Header Bar */}
        <div className="flex items-center justify-between px-4 py-3 min-h-[60px]">
          {/* Left: Hamburger Menu */}
          <button
            onClick={toggleMenu}
            className="text-[#131D4D] hover:text-gray-600"
            aria-label="Toggle menu"
          >
            <svg
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 6h16M4 12h16M4 18h16"
              ></path>
            </svg>
          </button>

          {/* Center: Mobile Logo */}
          <div className="flex items-center">
            <a href="/" onClick={() => setIsMenuOpen(false)}>
              <img
                src="/assets/imgs/logo.svg"
                alt="mailpallet-logo"
                width={100}
                height={30}
                className="h-auto"
                style={{
                  filter:
                    "brightness(0) saturate(100%) invert(7%) sepia(36%) saturate(1953%) hue-rotate(228deg) brightness(98%) contrast(97%)",
                }}
              />
            </a>
          </div>

          {/* Right: User and Cart Icons */}
          <div className="flex items-center space-x-4">
            {isLoggedIn ? (
              <>
                <div className="relative profile-dropdown-container hidden">
                  <div
                    onClick={toggleProfileDropdown}
                    className="cursor-pointer"
                    role="button"
                    tabIndex={0}
                    aria-expanded={isProfileDropdownOpen}
                    onKeyDown={(e) =>
                      e.key === "Enter" && toggleProfileDropdown()
                    }
                  >
                    <Avatar className="w-8 h-8 hover:ring-2 hover:ring-blue-300 transition-all">
                      <AvatarFallback className="bg-blue-600 text-white font-bold text-xs">
                        {displayInitials}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                </div>

                {/* Cart for logged-in users (mobile) */}
                <div className="flex flex-col items-center">
                  <Link
                    href="/home/<USER>/cart"
                    className="flex items-center justify-center p-2 hover:bg-blue-100 hover:rounded-md transition-colors relative"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <ShoppingCart className="w-6 h-6 text-[#131D4D]" />
                    {cartQuantity > 0 && (
                      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                        {cartQuantity}
                      </span>
                    )}
                  </Link>
                </div>
              </>
            ) : (
              <>
                {/* Show cart for logged-out users only if it has items */}
                {cartQuantity > 0 && (
                  <div className="flex flex-col items-center">
                    <Link
                      href="/home/<USER>/cart"
                      className="flex items-center justify-center p-2 hover:bg-blue-100 hover:rounded-md transition-colors relative"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <ShoppingCart className="w-6 h-6 text-[#131D4D]" />
                      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                        {cartQuantity}
                      </span>
                    </Link>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Mobile Search Bar */}
        {isLoggedIn && (
          <div className="px-4 pb-3">
            <div className="flex items-center bg-white rounded-lg overflow-hidden border border-gray-300">
              <Input
                type="text"
                placeholder="iPhone 16e..."
                className="flex-1 border-0 focus:ring-0 focus:outline-none rounded-none bg-white text-gray-800"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              />
              <Button
                className="bg-primary hover:bg-blue-800 text-white border-0 rounded-none px-4"
                onClick={handleSearch}
              >
                <Search className="w-5 h-5" />
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Mobile Menu */}
      <div
        id="mobile-menu"
        className="hidden sm:hidden px-4 py-2 bg-slate-200 text-center"
      >
        {!isLoggedIn && (
          <>
            <a
              href="/home/<USER>"
              className={`block py-2 ${isActivePath("/home/<USER>")}`}
              onClick={toggleMenu}
            >
              Shop
            </a>
            <a
              href="/about"
              className={`block py-2 ${isActivePath("/about")}`}
              onClick={toggleMenu}
            >
              About
            </a>
            <a
              href="/services"
              className={`block py-2 ${isActivePath("/services")}`}
              onClick={toggleMenu}
            >
              Services
            </a>
            <a
              href="/shipping"
              className={`block py-2 ${isActivePath("/shipping")}`}
              onClick={toggleMenu}
            >
              Shipping Calculator
            </a>
            <a
              href="/get-started"
              className={isActivePath("/get-started")}
              onClick={toggleMenu}
            >
              <MPSparklesText
                text="How To Use MailPallet"
                className="font-normal text-md"
              />
            </a>
          </>
        )}

        {isLoggedIn ? (
          <div className="mt-4 bg-blue-50 rounded-xl">
            <div className="p-4">
              <Link
                href="/home/<USER>"
                className="flex items-center justify-center space-x-3 hover:bg-blue-100 rounded-lg p-2 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <Avatar className="w-8 h-8">
                  <AvatarFallback className="bg-blue-600 text-white font-bold text-sm">
                    {userData?.first_name?.[0] ?? ""}
                    {userData?.last_name?.[0] ?? ""}
                  </AvatarFallback>
                </Avatar>
                <div className="text-left">
                  <p className="font-semibold text-sm">
                    {userData?.first_name} {userData?.last_name}
                  </p>
                  <p className="text-xs text-gray-600">{userData?.email}</p>
                </div>
              </Link>
            </div>

            <div className="border-t border-blue-100">
              <Link
                href="/home/<USER>/my-orders"
                className="flex items-center justify-center space-x-3 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <Container className="w-4 h-4 text-gray-600" />
                <span className="text-sm">My Orders</span>
              </Link>

              <Link
                href="/home/<USER>"
                className="flex items-center justify-center space-x-2 p-3 hover:bg-blue-100 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <Home className="w-4 h-4 text-gray-600" />
                <span className="text-sm">Virtual Address</span>
              </Link>

              {isAdmin && (
                <Link
                  href="/admin/dashboard"
                  className="flex items-center justify-center space-x-2 p-3 hover:bg-green-50 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Shield className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600 font-medium">
                    Admin Dashboard
                  </span>
                </Link>
              )}

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <button className="w-full flex items-center justify-center space-x-2 p-3 text-red-600 hover:bg-red-50 transition-colors border-t border-blue-100">
                    <LogOut className="w-4 h-4 text-red-500" />
                    <span className="text-sm">Log out</span>
                  </button>
                </AlertDialogTrigger>
                <AlertDialogContent className="max-w-[425px] w-[90vw] rounded-xl">
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you sure you want to log out?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      You'll need to sign in again to access your account.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter className="flex items-center">
                    <AlertDialogCancel className="w-full sm:w-auto">
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleSignout}
                      className="w-full sm:w-auto bg-red-600 hover:bg-red-700"
                    >
                      Log out
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        ) : (
          <>
            {/* If logged out, still show cart item in the mobile menu area if there are items */}
            {cartQuantity > 0 && (
              <div className="py-2">
                <Link
                  href="/home/<USER>/cart"
                  className="block py-2"
                  onClick={toggleMenu}
                >
                  <div className="inline-flex items-center space-x-2 justify-center">
                    <ShoppingCart className="w-5 h-5" />
                    <span className="text-sm font-medium">
                      Cart ({cartQuantity})
                    </span>
                  </div>
                </Link>
              </div>
            )}

            <a
              href="/login"
              className="block py-2 mt-2 bg-primary hover:bg-blue-800 text-white font-bold rounded"
              onClick={() => setIsMenuOpen(false)}
            >
              Register/Login
            </a>
          </>
        )}
      </div>
    </header>
  );
}
